'use client'

import { useState } from 'react'
import { Sidebar } from './Sidebar'
import { MainContent } from './MainContent'
import { Player } from './Player'
import { TopBar } from './TopBar'

export function Dashboard() {
  const [currentTrack, setCurrentTrack] = useState(null)
  const [isPlaying, setIsPlaying] = useState(false)

  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-red-50 to-red-100">
      {/* Top Bar */}
      <TopBar />
      
      {/* Main Layout */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <Sidebar />
        
        {/* Main Content */}
        <MainContent 
          onTrackSelect={setCurrentTrack}
          currentTrack={currentTrack}
        />
      </div>
      
      {/* Bottom Player */}
      <Player 
        currentTrack={currentTrack}
        isPlaying={isPlaying}
        onPlayPause={() => setIsPlaying(!isPlaying)}
      />
    </div>
  )
}
