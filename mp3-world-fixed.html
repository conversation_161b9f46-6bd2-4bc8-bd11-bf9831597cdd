<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MP3 World - Music Streaming</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #FFF5F5 0%, #FED7D7 50%, #FEB2B2 100%);
            min-height: 100vh;
        }
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        .red-gradient {
            background: linear-gradient(135deg, #E53E3E 0%, #FC8181 100%);
        }
        .audio-visualizer {
            display: flex;
            align-items: center;
            gap: 2px;
        }
        .audio-visualizer span {
            width: 3px;
            height: 12px;
            background: #E53E3E;
            border-radius: 1px;
            animation: wave 1s ease-in-out infinite;
        }
        .audio-visualizer span:nth-child(2) { animation-delay: 0.1s; }
        .audio-visualizer span:nth-child(3) { animation-delay: 0.2s; }
        @keyframes wave {
            0%, 100% { height: 6px; }
            50% { height: 16px; }
        }
    </style>
</head>
<body>
    <!-- Login Page -->
    <div id="loginPage" class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 red-gradient rounded-full mb-4 shadow-lg">
                    <i data-lucide="music" class="w-8 h-8 text-white"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Welcome to MP3 World</h1>
                <p class="text-gray-600">Sign in to access your music library</p>
            </div>

            <!-- Login Card -->
            <div class="glass rounded-2xl p-8 shadow-xl">
                <!-- Terms Notice -->
                <div class="text-xs text-gray-600 mb-6 text-center">
                    By continuing, you agree to our <a href="#" class="text-blue-600 underline">Terms of Use</a> and acknowledge our <a href="#" class="text-blue-600 underline">Privacy Policy</a>.
                </div>
                
                <!-- Checkbox -->
                <div class="flex items-start gap-3 mb-6">
                    <input type="checkbox" id="marketingEmails" class="mt-1 w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500">
                    <label for="marketingEmails" class="text-sm text-gray-600">
                        Click here if you do not want to receive marketing emails from MP3 World and affiliates
                    </label>
                </div>
                
                <div class="space-y-4">
                    <!-- Google Sign In Button -->
                    <button onclick="authenticateWithGoogle()" class="w-full flex items-center justify-center gap-3 py-4 px-6 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 shadow-sm">
                        <svg width="20" height="20" viewBox="0 0 24 24">
                            <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Continue with Google
                    </button>

                    <!-- Apple Sign In Button -->
                    <button onclick="authenticateWithApple()" class="w-full flex items-center justify-center gap-3 py-4 px-6 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 bg-black hover:bg-gray-800 text-white shadow-sm">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                        </svg>
                        Continue with Apple
                    </button>
                    
                    <!-- Divider -->
                    <div class="relative my-6">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">or</span>
                        </div>
                    </div>
                    
                    <!-- Email Input -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Enter email</label>
                        <input type="email" id="emailInput" placeholder="Enter your email address" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent">
                    </div>
                    
                    <!-- Continue with Email Button -->
                    <button onclick="authenticateWithEmail()" class="w-full py-4 px-6 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 bg-red-600 hover:bg-red-700 text-white shadow-sm">
                        Continue with email
                    </button>
                    
                    <!-- Custom ID Option -->
                    <div class="relative my-6">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">or use custom ID</span>
                        </div>
                    </div>
                    
                    <!-- Custom ID Form -->
                    <div id="customIdForm" class="space-y-3">
                        <input type="text" id="customUserId" placeholder="Enter your custom user ID" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"/>
                        <input type="text" id="customUserName" placeholder="Enter your display name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"/>
                        <input type="email" id="customUserEmail" placeholder="Enter your email address" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"/>
                        <button onclick="authenticateWithCustomId()" class="w-full py-4 px-6 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 bg-gray-800 hover:bg-gray-900 text-white shadow-sm">
                            Login with Custom ID
                        </button>
                    </div>
                </div>

                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-500">
                        By signing in, you agree to our
                        <a href="#" class="text-red-600 hover:text-red-800 underline">Terms of Service</a>
                        and
                        <a href="#" class="text-red-600 hover:text-red-800 underline">Privacy Policy</a>
                    </p>
                </div>
            </div>

            <!-- Features -->
            <div class="mt-8 grid grid-cols-3 gap-4 text-center">
                <div class="glass rounded-xl p-4">
                    <div class="w-8 h-8 red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
                        <i data-lucide="music" class="w-4 h-4 text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">Unlimited Music</p>
                </div>
                <div class="glass rounded-xl p-4">
                    <div class="w-8 h-8 red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
                        <i data-lucide="headphones" class="w-4 h-4 text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">High Quality</p>
                </div>
                <div class="glass rounded-xl p-4">
                    <div class="w-8 h-8 red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
                        <i data-lucide="download" class="w-4 h-4 text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">Offline Mode</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard (Initially hidden) -->
    <div id="dashboard" class="h-screen flex flex-col hidden">
        <!-- Top Bar -->
        <div class="h-16 glass border-b border-white/20 flex items-center justify-between px-6">
            <!-- Search Bar -->
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                    <input type="text" placeholder="Search for songs, artists, or albums..." class="w-full pl-10 pr-4 py-2 bg-white/20 border border-white/30 rounded-full text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"/>
                </div>
            </div>

            <!-- User Menu -->
            <div class="flex items-center gap-4">
                <div class="flex items-center gap-2 p-2 rounded-full hover:bg-white/20">
                    <img id="userAvatar" src="" alt="User Avatar" class="w-8 h-8 rounded-full hidden">
                    <div id="defaultAvatar" class="w-8 h-8 red-gradient rounded-full flex items-center justify-center">
                        <i data-lucide="user" class="w-4 h-4 text-white"></i>
                    </div>
                    <div class="flex flex-col">
                        <span id="userName" class="text-sm font-medium text-gray-700">Demo User</span>
                        <span id="userProvider" class="text-xs text-green-600 hidden">✓ via Google</span>
                    </div>
                    <button onclick="showLogin()" class="text-xs text-red-600 hover:text-red-800 ml-2">Logout</button>
                </div>
            </div>
        </div>
        
        <!-- Main Layout -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Sidebar -->
            <div class="w-64 glass border-r border-white/20 flex flex-col">
                <!-- Logo -->
                <div class="p-6 border-b border-white/20">
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 red-gradient rounded-lg flex items-center justify-center">
                            <i data-lucide="music" class="w-5 h-5 text-white"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-800">MP3 World</span>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="flex-1 overflow-y-auto p-4 space-y-6">
                    <div class="space-y-2">
                        <button onclick="showView('home')" class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left bg-red-600 text-white">
                            <i data-lucide="home" class="w-5 h-5"></i>
                            <span class="font-medium">Home</span>
                        </button>
                        <button onclick="showView('search')" class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-700 hover:bg-white/30">
                            <i data-lucide="search" class="w-5 h-5"></i>
                            <span class="font-medium">Search</span>
                        </button>
                        <button onclick="showView('library')" class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-700 hover:bg-white/30">
                            <i data-lucide="library" class="w-5 h-5"></i>
                            <span class="font-medium">Your Library</span>
                        </button>
                    </div>

                    <div>
                        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Library</h3>
                        <div class="space-y-1">
                            <button onclick="showView('liked')" class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-600 hover:bg-white/30">
                                <i data-lucide="heart" class="w-4 h-4"></i>
                                <span class="text-sm">Liked Songs</span>
                                <span id="likedCount" class="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full ml-auto">0</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="flex-1 overflow-y-auto p-8">
                <div id="contentArea">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Good afternoon<span id="welcomeName"></span></h1>
                    <p class="text-gray-600 mb-8">Ready to discover your next favorite song?</p>
                    
                    <div class="glass rounded-xl overflow-hidden">
                        <div class="p-4 border-b border-white/20">
                            <h2 class="text-xl font-bold text-gray-900">Your Music Library</h2>
                        </div>
                        <div id="musicList" class="divide-y divide-white/10">
                            <!-- Music tracks will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Bottom Player -->
        <div class="h-20 glass border-t border-white/20 flex items-center justify-between px-4">
            <div class="flex items-center gap-3 w-1/4">
                <div class="w-14 h-14 red-gradient rounded-lg flex items-center justify-center">
                    <i data-lucide="music" class="w-6 h-6 text-white"></i>
                </div>
                <div class="min-w-0">
                    <p id="currentSong" class="font-medium text-gray-900 truncate">Select a song to play</p>
                    <p id="currentArtist" class="text-sm text-gray-600 truncate">No song selected</p>
                </div>
            </div>
            
            <div class="flex items-center gap-4">
                <button onclick="previousSong()" class="p-2 text-gray-700 hover:text-red-600">
                    <i data-lucide="skip-back" class="w-5 h-5"></i>
                </button>
                <button id="playBtn" onclick="togglePlay()" class="w-10 h-10 rounded-full red-gradient text-white flex items-center justify-center hover:scale-105">
                    <i data-lucide="play" class="w-5 h-5 ml-1"></i>
                </button>
                <button onclick="nextSong()" class="p-2 text-gray-700 hover:text-red-600">
                    <i data-lucide="skip-forward" class="w-5 h-5"></i>
                </button>
            </div>
            
            <div class="w-1/4"></div>
        </div>
    </div>

    <script>
        // Music library
        const musicLibrary = [
            { id: 1, title: "A Ninode en daivame", artist: "Unknown Artist", file: "./A Ninode en daivame.wav2.wav" },
            { id: 2, title: "A Paro", artist: "Unknown Artist", file: "./A Paro.wav" },
            { id: 3, title: "AnnnnRosh", artist: "Unknown Artist", file: "./AnnnnRosh.mp3" },
            { id: 4, title: "Aremb Final out", artist: "Unknown Artist", file: "./Aremb Final out.mp3" },
            { id: 5, title: "Arosh", artist: "Unknown Artist", file: "./Arosh.wav" },
            { id: 6, title: "AtoZ Remembrance Hillsong", artist: "Hillsong", file: "./AtoZRemembrance hillsong.wav" },
            { id: 7, title: "Chattan Cover 2", artist: "Unknown Artist", file: "./Chattan Cover 2.wav" },
            { id: 8, title: "A New Wine", artist: "Unknown Artist", file: "./anew wine.wav" },
            { id: 9, title: "Avega Tere Lahu Ka", artist: "Unknown Artist", file: "./avega Tere lahu ka .wav" }
        ];

        let currentAudio = null;
        let isPlaying = false;
        let currentTrackIndex = 0;
        let likedSongs = [];

        // Authentication Functions
        function authenticateWithGoogle() {
            console.log('Google authentication started');
            showGoogleAuthFlow();
        }

        function showGoogleAuthFlow() {
            // Show loading screen
            const loadingDiv = document.createElement('div');
            loadingDiv.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); display: flex; align-items: center; justify-content: center; z-index: 9999;">
                    <div style="background: white; padding: 40px; border-radius: 12px; text-align: center; max-width: 400px;">
                        <svg width="48" height="48" viewBox="0 0 24 24" style="margin: 0 auto 20px;">
                            <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        <h3 style="margin: 0 0 10px; color: #202124; font-family: Arial, sans-serif;">Sign in with Google</h3>
                        <p style="margin: 0 0 20px; color: #5f6368; font-family: Arial, sans-serif;">Authenticating with Libin Lalu...</p>
                        <div style="width: 32px; height: 4px; background: #f0f0f0; border-radius: 2px; margin: 0 auto; overflow: hidden;">
                            <div style="width: 100%; height: 100%; background: #4285f4; border-radius: 2px; animation: progress 1s ease-in-out;"></div>
                        </div>
                    </div>
                </div>
                <style>
                    @keyframes progress { 0% { transform: translateX(-100%); } 100% { transform: translateX(0); } }
                </style>
            `;
            document.body.appendChild(loadingDiv);
            
            setTimeout(() => {
                document.body.removeChild(loadingDiv);
                completeGoogleAuth();
            }, 1000);
        }

        function completeGoogleAuth() {
            const googleUser = {
                name: 'Libin Lalu',
                email: '<EMAIL>',
                googleId: 'google_' + Date.now(),
                profilePicture: null,
                authMethod: 'google',
                provider: 'google',
                verified_email: true,
                loginTime: new Date().toISOString()
            };
            
            localStorage.setItem('mp3_world_user', JSON.stringify(googleUser));
            updateUserInterface(googleUser);
            showDashboard();
            
            setTimeout(() => {
                showNotification('🎉 Successfully signed in with Google!');
            }, 500);
        }

        function authenticateWithApple() {
            const appleUser = {
                name: 'Apple User',
                email: '<EMAIL>',
                appleId: 'apple_' + Date.now(),
                profilePicture: null,
                authMethod: 'apple',
                provider: 'apple',
                verified_email: true,
                loginTime: new Date().toISOString()
            };
            
            localStorage.setItem('mp3_world_user', JSON.stringify(appleUser));
            updateUserInterface(appleUser);
            showDashboard();
            
            setTimeout(() => {
                showNotification('🍎 Successfully signed in with Apple!');
            }, 500);
        }

        function authenticateWithEmail() {
            const email = document.getElementById('emailInput').value.trim();
            if (!email) {
                alert('Please enter your email address');
                return;
            }
            
            if (!email.includes('@')) {
                alert('Please enter a valid email address');
                return;
            }
            
            const emailUser = {
                name: email.split('@')[0],
                email: email,
                emailId: 'email_' + Date.now(),
                profilePicture: null,
                authMethod: 'email',
                provider: 'email',
                verified_email: false,
                loginTime: new Date().toISOString()
            };
            
            localStorage.setItem('mp3_world_user', JSON.stringify(emailUser));
            updateUserInterface(emailUser);
            showDashboard();
            
            setTimeout(() => {
                showNotification('📧 Successfully signed in with email!');
            }, 500);
        }

        function authenticateWithCustomId() {
            const customId = document.getElementById('customUserId').value.trim();
            const customName = document.getElementById('customUserName').value.trim();
            const customEmail = document.getElementById('customUserEmail').value.trim();
            
            if (!customId || !customName || !customEmail) {
                alert('❌ Please fill in all required fields (ID, Name, Email)');
                return;
            }
            
            const customUser = {
                id: "custom_" + customId,
                customId: customId,
                name: customName,
                email: customEmail,
                picture: `https://ui-avatars.com/api/?name=${encodeURIComponent(customName)}&background=e53e3e&color=fff&size=200`,
                verified_email: true,
                authMethod: "custom",
                provider: "custom",
                loginTime: new Date().toISOString()
            };
            
            localStorage.setItem('mp3_world_user', JSON.stringify(customUser));
            updateUserInterface(customUser);
            showDashboard();
            
            setTimeout(() => {
                showNotification('🎉 Successfully authenticated with Custom ID!');
            }, 500);
        }

        function updateUserInterface(user) {
            document.getElementById('userName').textContent = user.name;
            if (user.provider) {
                document.getElementById('userProvider').textContent = '✓ via ' + user.provider;
                document.getElementById('userProvider').classList.remove('hidden');
            }
            
            if (user.picture) {
                document.getElementById('userAvatar').src = user.picture;
                document.getElementById('userAvatar').classList.remove('hidden');
                document.getElementById('defaultAvatar').classList.add('hidden');
            }
            
            const firstName = user.name.split(' ')[0];
            document.getElementById('welcomeName').textContent = ', ' + firstName;
        }

        function showDashboard() {
            document.getElementById('loginPage').classList.add('hidden');
            document.getElementById('dashboard').classList.remove('hidden');
            loadMusicLibrary();
            lucide.createIcons();
        }

        function showLogin() {
            localStorage.removeItem('mp3_world_user');
            document.getElementById('dashboard').classList.add('hidden');
            document.getElementById('loginPage').classList.remove('hidden');
            lucide.createIcons();
        }

        function loadMusicLibrary() {
            const musicList = document.getElementById('musicList');
            musicList.innerHTML = '';
            
            musicLibrary.forEach((track, index) => {
                const trackElement = document.createElement('div');
                trackElement.className = 'p-4 hover:bg-white/20 transition-colors cursor-pointer group';
                trackElement.onclick = () => playTrack(index);
                
                trackElement.innerHTML = `
                    <div class="flex items-center gap-4">
                        <span class="text-gray-600 group-hover:hidden w-8">${index + 1}</span>
                        <i data-lucide="play" class="w-4 h-4 text-red-600 hidden group-hover:block w-8"></i>
                        <div class="flex-1">
                            <p class="font-medium text-gray-900">${track.title}</p>
                            <p class="text-sm text-gray-600">${track.artist}</p>
                        </div>
                        <button onclick="toggleLike(${track.id}); event.stopPropagation();" class="p-2 opacity-0 group-hover:opacity-100 hover:text-red-600">
                            <i data-lucide="heart" class="w-4 h-4"></i>
                        </button>
                    </div>
                `;
                
                musicList.appendChild(trackElement);
            });
            
            lucide.createIcons();
        }

        function playTrack(index) {
            currentTrackIndex = index;
            const track = musicLibrary[index];
            
            if (currentAudio) {
                currentAudio.pause();
            }
            
            currentAudio = new Audio(track.file);
            document.getElementById('currentSong').textContent = track.title;
            document.getElementById('currentArtist').textContent = track.artist;
            
            currentAudio.play().then(() => {
                isPlaying = true;
                document.querySelector('#playBtn i').setAttribute('data-lucide', 'pause');
                document.querySelector('#playBtn i').classList.remove('ml-1');
                lucide.createIcons();
            }).catch(error => {
                alert('Error playing audio file: ' + track.title);
            });
        }

        function togglePlay() {
            if (!currentAudio) {
                if (musicLibrary.length > 0) {
                    playTrack(0);
                }
                return;
            }
            
            if (isPlaying) {
                currentAudio.pause();
                isPlaying = false;
                document.querySelector('#playBtn i').setAttribute('data-lucide', 'play');
                document.querySelector('#playBtn i').classList.add('ml-1');
            } else {
                currentAudio.play();
                isPlaying = true;
                document.querySelector('#playBtn i').setAttribute('data-lucide', 'pause');
                document.querySelector('#playBtn i').classList.remove('ml-1');
            }
            lucide.createIcons();
        }

        function previousSong() {
            let newIndex = currentTrackIndex - 1;
            if (newIndex < 0) newIndex = musicLibrary.length - 1;
            playTrack(newIndex);
        }

        function nextSong() {
            let newIndex = currentTrackIndex + 1;
            if (newIndex >= musicLibrary.length) newIndex = 0;
            playTrack(newIndex);
        }

        function toggleLike(trackId) {
            const index = likedSongs.indexOf(trackId);
            if (index > -1) {
                likedSongs.splice(index, 1);
            } else {
                likedSongs.push(trackId);
            }
            document.getElementById('likedCount').textContent = likedSongs.length;
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.innerHTML = `
                <div style="position: fixed; top: 20px; right: 20px; background: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 10000; border-left: 4px solid #E53E3E;">
                    <p style="margin: 0; color: #333; font-family: Arial, sans-serif;">${message}</p>
                </div>
            `;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }

        function showView(view) {
            console.log('Showing view:', view);
        }

        // Initialize
        window.onload = function() {
            const storedUser = localStorage.getItem('mp3_world_user');
            if (storedUser) {
                const user = JSON.parse(storedUser);
                updateUserInterface(user);
                showDashboard();
            } else {
                lucide.createIcons();
            }
        };
    </script>
</body>
</html>
