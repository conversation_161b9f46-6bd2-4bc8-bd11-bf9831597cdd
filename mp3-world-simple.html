<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MP3 World - Music Streaming</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #FFF5F5 0%, #FED7D7 50%, #FEB2B2 100%);
            min-height: 100vh;
        }
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        .red-gradient {
            background: linear-gradient(135deg, #E53E3E 0%, #FC8181 100%);
        }
    </style>
</head>
<body>
    <!-- Login Page -->
    <div id="loginPage" class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 red-gradient rounded-full mb-4 shadow-lg">
                    <i data-lucide="music" class="w-8 h-8 text-white"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Welcome to MP3 World</h1>
                <p class="text-gray-600">Sign in to access your music library</p>
            </div>

            <div class="glass rounded-2xl p-8 shadow-xl">
                <div class="space-y-4">
                    <button onclick="loginWithGoogle()" class="w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 shadow-md">
                        <i class="fab fa-google w-5 h-5 text-red-500"></i>
                        Continue with Google
                    </button>
                    
                    <button onclick="loginWithCustom()" class="w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 red-gradient text-white shadow-md">
                        <i data-lucide="user-check" class="w-5 h-5"></i>
                        Login with Custom ID
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard -->
    <div id="dashboard" class="h-screen flex flex-col hidden">
        <!-- Top Bar -->
        <div class="h-16 glass border-b border-white/20 flex items-center justify-between px-6">
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                    <input
                        type="text"
                        placeholder="Search for songs, artists, or albums..."
                        class="w-full pl-10 pr-4 py-2 bg-white/20 border border-white/30 rounded-full text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                        oninput="searchMusic(this.value)"
                    />
                </div>
            </div>
            <div class="flex items-center gap-4">
                <span id="userName" class="text-sm font-medium text-gray-700">User</span>
                <button onclick="logout()" class="text-xs text-red-600 hover:text-red-800">Logout</button>
            </div>
        </div>
        
        <!-- Main Layout -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Sidebar -->
            <div class="w-64 glass border-r border-white/20 flex flex-col">
                <div class="p-6 border-b border-white/20">
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 red-gradient rounded-lg flex items-center justify-center">
                            <i data-lucide="music" class="w-5 h-5 text-white"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-800">MP3 World</span>
                    </div>
                </div>
                
                <div class="flex-1 overflow-y-auto p-4 space-y-6">
                    <div class="space-y-2">
                        <button onclick="showHome()" class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left bg-red-600 text-white">
                            <i data-lucide="home" class="w-5 h-5"></i>
                            <span class="font-medium">Home</span>
                        </button>
                        <button onclick="showSearch()" class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-700 hover:bg-white/30">
                            <i data-lucide="search" class="w-5 h-5"></i>
                            <span class="font-medium">Search</span>
                        </button>
                        <button onclick="showLibrary()" class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-700 hover:bg-white/30">
                            <i data-lucide="library" class="w-5 h-5"></i>
                            <span class="font-medium">Your Library</span>
                        </button>
                    </div>
                    
                    <div>
                        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Library</h3>
                        <div class="space-y-1">
                            <button onclick="showLikedSongs()" class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-600 hover:bg-white/30">
                                <i data-lucide="heart" class="w-4 h-4"></i>
                                <span class="text-sm">Liked Songs</span>
                                <span id="likedCount" class="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full ml-auto">0</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="flex-1 overflow-y-auto p-8">
                <div id="contentArea">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Good afternoon</h1>
                    <p class="text-gray-600 mb-8">Ready to discover your next favorite song?</p>
                    
                    <div class="glass rounded-xl overflow-hidden">
                        <div class="p-4 border-b border-white/20">
                            <h2 class="text-xl font-bold text-gray-900">Your Music Library</h2>
                        </div>
                        <div id="musicList" class="divide-y divide-white/10">
                            <!-- Music tracks will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Bottom Player -->
        <div class="h-20 glass border-t border-white/20 flex items-center justify-between px-4">
            <div class="flex items-center gap-3 w-1/4">
                <div class="w-14 h-14 red-gradient rounded-lg flex items-center justify-center">
                    <i data-lucide="music" class="w-6 h-6 text-white"></i>
                </div>
                <div class="min-w-0">
                    <p id="currentSong" class="font-medium text-gray-900 truncate">Select a song to play</p>
                    <p id="currentArtist" class="text-sm text-gray-600 truncate">No song selected</p>
                </div>
            </div>
            
            <div class="flex items-center gap-4">
                <button onclick="previousSong()" class="p-2 text-gray-700 hover:text-red-600">
                    <i data-lucide="skip-back" class="w-5 h-5"></i>
                </button>
                <button id="playBtn" onclick="togglePlay()" class="w-10 h-10 rounded-full red-gradient text-white flex items-center justify-center hover:scale-105">
                    <i data-lucide="play" class="w-5 h-5 ml-1"></i>
                </button>
                <button onclick="nextSong()" class="p-2 text-gray-700 hover:text-red-600">
                    <i data-lucide="skip-forward" class="w-5 h-5"></i>
                </button>
            </div>
            
            <div class="w-1/4"></div>
        </div>
    </div>

    <script>
        // Music library
        const musicLibrary = [
            { id: 1, title: "A Ninode en daivame", artist: "Unknown Artist", file: "./A Ninode en daivame.wav2.wav" },
            { id: 2, title: "A Paro", artist: "Unknown Artist", file: "./A Paro.wav" },
            { id: 3, title: "AnnnnRosh", artist: "Unknown Artist", file: "./AnnnnRosh.mp3" },
            { id: 4, title: "Aremb Final out", artist: "Unknown Artist", file: "./Aremb Final out.mp3" },
            { id: 5, title: "Arosh", artist: "Unknown Artist", file: "./Arosh.wav" },
            { id: 6, title: "AtoZ Remembrance Hillsong", artist: "Hillsong", file: "./AtoZRemembrance hillsong.wav" },
            { id: 7, title: "Chattan Cover 2", artist: "Unknown Artist", file: "./Chattan Cover 2.wav" },
            { id: 8, title: "A New Wine", artist: "Unknown Artist", file: "./anew wine.wav" },
            { id: 9, title: "Avega Tere Lahu Ka", artist: "Unknown Artist", file: "./avega Tere lahu ka .wav" }
        ];

        let currentAudio = null;
        let isPlaying = false;
        let currentTrackIndex = 0;
        let likedSongs = [];

        function loginWithGoogle() {
            document.getElementById('userName').textContent = 'Google User';
            showDashboard();
        }

        function loginWithCustom() {
            const name = prompt('Enter your name:');
            if (name) {
                document.getElementById('userName').textContent = name;
                showDashboard();
            }
        }

        function showDashboard() {
            document.getElementById('loginPage').classList.add('hidden');
            document.getElementById('dashboard').classList.remove('hidden');
            loadMusicLibrary();
            lucide.createIcons();
        }

        function logout() {
            document.getElementById('dashboard').classList.add('hidden');
            document.getElementById('loginPage').classList.remove('hidden');
            lucide.createIcons();
        }

        function loadMusicLibrary() {
            const musicList = document.getElementById('musicList');
            musicList.innerHTML = '';
            
            musicLibrary.forEach((track, index) => {
                const trackElement = document.createElement('div');
                trackElement.className = 'p-4 hover:bg-white/20 transition-colors cursor-pointer group';
                trackElement.onclick = () => playTrack(index);
                
                trackElement.innerHTML = `
                    <div class="flex items-center gap-4">
                        <span class="text-gray-600 group-hover:hidden w-8">${index + 1}</span>
                        <i data-lucide="play" class="w-4 h-4 text-red-600 hidden group-hover:block w-8"></i>
                        <div class="flex-1">
                            <p class="font-medium text-gray-900">${track.title}</p>
                            <p class="text-sm text-gray-600">${track.artist}</p>
                        </div>
                        <button onclick="toggleLike(${track.id}); event.stopPropagation();" class="p-2 opacity-0 group-hover:opacity-100 hover:text-red-600">
                            <i data-lucide="heart" class="w-4 h-4"></i>
                        </button>
                    </div>
                `;
                
                musicList.appendChild(trackElement);
            });
            
            lucide.createIcons();
        }

        function playTrack(index) {
            currentTrackIndex = index;
            const track = musicLibrary[index];
            
            if (currentAudio) {
                currentAudio.pause();
            }
            
            currentAudio = new Audio(track.file);
            document.getElementById('currentSong').textContent = track.title;
            document.getElementById('currentArtist').textContent = track.artist;
            
            currentAudio.play().then(() => {
                isPlaying = true;
                document.querySelector('#playBtn i').setAttribute('data-lucide', 'pause');
                document.querySelector('#playBtn i').classList.remove('ml-1');
                lucide.createIcons();
            }).catch(error => {
                alert('Error playing audio file: ' + track.title);
            });
        }

        function togglePlay() {
            if (!currentAudio) {
                if (musicLibrary.length > 0) {
                    playTrack(0);
                }
                return;
            }
            
            if (isPlaying) {
                currentAudio.pause();
                isPlaying = false;
                document.querySelector('#playBtn i').setAttribute('data-lucide', 'play');
                document.querySelector('#playBtn i').classList.add('ml-1');
            } else {
                currentAudio.play();
                isPlaying = true;
                document.querySelector('#playBtn i').setAttribute('data-lucide', 'pause');
                document.querySelector('#playBtn i').classList.remove('ml-1');
            }
            lucide.createIcons();
        }

        function previousSong() {
            let newIndex = currentTrackIndex - 1;
            if (newIndex < 0) newIndex = musicLibrary.length - 1;
            playTrack(newIndex);
        }

        function nextSong() {
            let newIndex = currentTrackIndex + 1;
            if (newIndex >= musicLibrary.length) newIndex = 0;
            playTrack(newIndex);
        }

        function toggleLike(trackId) {
            const index = likedSongs.indexOf(trackId);
            if (index > -1) {
                likedSongs.splice(index, 1);
            } else {
                likedSongs.push(trackId);
            }
            document.getElementById('likedCount').textContent = likedSongs.length;
        }

        function searchMusic(query) {
            // Simple search implementation
            console.log('Searching for:', query);
        }

        function showHome() { console.log('Show Home'); }
        function showSearch() { console.log('Show Search'); }
        function showLibrary() { console.log('Show Library'); }
        function showLikedSongs() { console.log('Show Liked Songs'); }

        window.onload = function() {
            lucide.createIcons();
        };
    </script>
</body>
</html>
