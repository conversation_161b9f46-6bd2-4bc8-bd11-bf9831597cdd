# Google OAuth Setup Guide for MP3 World

This guide will help you set up Google authentication to fetch user's Google ID and enable login to their account.

## Step 1: Create Google Cloud Project

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/
   - Sign in with your Google account

2. **Create a New Project**
   - Click "Select a project" dropdown
   - Click "New Project"
   - Enter project name: "MP3 World"
   - Click "Create"

## Step 2: Enable Required APIs

1. **Enable Google+ API**
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API"
   - Click on it and press "Enable"

2. **Enable People API**
   - Search for "Google People API"
   - Click on it and press "Enable"

## Step 3: Configure OAuth Consent Screen

1. **Go to OAuth Consent Screen**
   - Navigate to "APIs & Services" > "OAuth consent screen"
   - Choose "External" user type
   - Click "Create"

2. **Fill App Information**
   - App name: `MP3 World`
   - User support email: Your email
   - App logo: Upload your app logo (optional)
   - App domain: `http://localhost:3000`
   - Developer contact: Your email
   - Click "Save and Continue"

3. **Scopes**
   - Click "Add or Remove Scopes"
   - Add these scopes:
     - `../auth/userinfo.email`
     - `../auth/userinfo.profile`
     - `openid`
   - Click "Save and Continue"

4. **Test Users** (for development)
   - Add your email address as a test user
   - Click "Save and Continue"

## Step 4: Create OAuth 2.0 Credentials

1. **Go to Credentials**
   - Navigate to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"

2. **Configure OAuth Client**
   - Application type: "Web application"
   - Name: "MP3 World Web Client"
   
3. **Add Authorized Redirect URIs**
   - Click "Add URI" under "Authorized redirect URIs"
   - Add: `http://localhost:3000/api/auth/callback/google`
   - For production, also add: `https://yourdomain.com/api/auth/callback/google`

4. **Create and Download**
   - Click "Create"
   - Copy the Client ID and Client Secret
   - Download the JSON file (optional, for backup)

## Step 5: Update Environment Variables

1. **Open `.env.local` file**
2. **Replace the Google OAuth variables:**
   ```env
   GOOGLE_CLIENT_ID=your-actual-client-id-here
   GOOGLE_CLIENT_SECRET=your-actual-client-secret-here
   ```

## Step 6: Install Dependencies and Run

1. **Install Node.js dependencies:**
   ```bash
   npm install
   ```

2. **Start the development server:**
   ```bash
   npm run dev
   ```

3. **Open your browser:**
   - Go to: http://localhost:3000
   - Click "Continue with Google"
   - Complete the OAuth flow

## What the Authentication Fetches

When a user logs in with Google, our app fetches:

- **Google ID** (`sub` field): Unique identifier for the user
- **Email Address**: User's primary email
- **Name**: Full name from Google profile
- **Profile Picture**: User's Google profile image
- **Email Verification Status**: Whether email is verified
- **Basic Profile Info**: Public profile information

## User Data Storage

The authentication system stores:

```typescript
{
  id: string,           // Internal user ID
  googleId: string,     // Google's unique user ID
  email: string,        // User's email
  name: string,         // User's full name
  image: string,        // Profile picture URL
  provider: 'google',   // Authentication provider
  verified_email: boolean // Email verification status
}
```

## Security Features

- **JWT Tokens**: Secure session management
- **HTTPS Required**: For production deployment
- **Scope Limitation**: Only requests necessary permissions
- **Token Expiration**: 30-day session timeout
- **Secure Callbacks**: Validated redirect URIs

## Testing the Integration

1. **Click "Continue with Google"**
2. **Complete Google OAuth flow**
3. **Check the user profile section** to see:
   - Google ID
   - Email verification status
   - Profile information
   - Authentication provider

## Troubleshooting

### Common Issues:

1. **"redirect_uri_mismatch" error**
   - Ensure redirect URI in Google Console matches exactly: `http://localhost:3000/api/auth/callback/google`

2. **"invalid_client" error**
   - Check that Client ID and Secret are correct in `.env.local`

3. **"access_denied" error**
   - Make sure your email is added as a test user in OAuth consent screen

4. **App not verified warning**
   - Normal for development; users can click "Advanced" > "Go to MP3 World (unsafe)"

### Debug Mode:

The app runs in debug mode during development and logs authentication details to the console.

## Production Deployment

For production:

1. **Update OAuth consent screen** to "In production"
2. **Add production domain** to authorized origins
3. **Update NEXTAUTH_URL** environment variable
4. **Use HTTPS** for all URLs
5. **Verify domain ownership** in Google Console

## Support

If you encounter issues:
1. Check the browser console for errors
2. Verify all environment variables are set
3. Ensure Google Cloud APIs are enabled
4. Check OAuth consent screen configuration
