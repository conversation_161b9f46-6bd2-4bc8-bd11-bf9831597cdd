'use client'

import { signIn } from 'next-auth/react'
import { Music, Chrome, Github, MessageCircle } from 'lucide-react'
import { Button } from '@/components/ui/Button'

export function LoginPage() {
  const providers = [
    {
      id: 'google',
      name: 'Google',
      icon: Chrome,
      color: 'bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 shadow-md',
      iconColor: 'text-red-500',
    },
    {
      id: 'apple',
      name: 'Apple',
      icon: Music,
      color: 'bg-black hover:bg-gray-900 text-white shadow-md',
      iconColor: 'text-white',
    },
    {
      id: 'github',
      name: 'GitHub',
      icon: Github,
      color: 'bg-gray-900 hover:bg-gray-800 text-white shadow-md',
      iconColor: 'text-white',
    },
    {
      id: 'discord',
      name: 'Discord',
      icon: MessageCircle,
      color: 'bg-indigo-600 hover:bg-indigo-700 text-white shadow-md',
      iconColor: 'text-white',
    },
  ]

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo and Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-red-gradient rounded-full mb-4 shadow-lg">
            <Music className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome to MP3 World
          </h1>
          <p className="text-gray-600">
            Sign in to access your music library
          </p>
        </div>

        {/* Login Card */}
        <div className="glass rounded-2xl p-8 shadow-xl">
          <div className="space-y-4">
            {providers.map((provider) => {
              const Icon = provider.icon
              return (
                <Button
                  key={provider.id}
                  onClick={() => signIn(provider.id)}
                  className={`w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 ${provider.color}`}
                >
                  <Icon className={`w-5 h-5 ${provider.iconColor || ''}`} />
                  Continue with {provider.name}
                </Button>
              )
            })}
          </div>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500">
              By signing in, you agree to our{' '}
              <a href="#" className="text-red-primary hover:text-red-dark underline">
                Terms of Service
              </a>{' '}
              and{' '}
              <a href="#" className="text-red-primary hover:text-red-dark underline">
                Privacy Policy
              </a>
            </p>
          </div>
        </div>

        {/* Features */}
        <div className="mt-8 grid grid-cols-3 gap-4 text-center">
          <div className="glass rounded-xl p-4">
            <div className="w-8 h-8 bg-red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
              <Music className="w-4 h-4 text-white" />
            </div>
            <p className="text-xs text-gray-600">Unlimited Music</p>
          </div>
          <div className="glass rounded-xl p-4">
            <div className="w-8 h-8 bg-red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
              <Music className="w-4 h-4 text-white" />
            </div>
            <p className="text-xs text-gray-600">High Quality</p>
          </div>
          <div className="glass rounded-xl p-4">
            <div className="w-8 h-8 bg-red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
              <Music className="w-4 h-4 text-white" />
            </div>
            <p className="text-xs text-gray-600">Offline Mode</p>
          </div>
        </div>
      </div>
    </div>
  )
}
