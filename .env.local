NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=mp3-world-secret-key-2024

# Google OAuth (Required for Google Authentication)
# Get these from: https://console.cloud.google.com/
# 1. Create a new project or select existing
# 2. Enable Google+ API and Google People API
# 3. Create OAuth 2.0 credentials
# 4. Add http://localhost:3000/api/auth/callback/google to authorized redirect URIs
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here

# GitHub OAuth (Optional)
GITHUB_ID=your-github-client-id
GITHUB_SECRET=your-github-client-secret

# Discord OAuth (Optional)
DISCORD_CLIENT_ID=your-discord-client-id
DISCORD_CLIENT_SECRET=your-discord-client-secret

# Apple OAuth (Optional)
APPLE_ID=your-apple-client-id
APPLE_SECRET=your-apple-client-secret
