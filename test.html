<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MP3 World Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #FFF5F5 0%, #FED7D7 50%, #FEB2B2 100%);
            min-height: 100vh;
        }
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        .red-gradient {
            background: linear-gradient(135deg, #E53E3E 0%, #FC8181 100%);
        }
    </style>
</head>
<body>
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 red-gradient rounded-full mb-4 shadow-lg">
                    <i data-lucide="music" class="w-8 h-8 text-white"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">MP3 World Test</h1>
                <p class="text-gray-600">Testing if the interface loads properly</p>
            </div>

            <div class="glass rounded-2xl p-8 shadow-xl">
                <div class="space-y-4">
                    <button onclick="testFunction()" class="w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 shadow-md">
                        <i class="fab fa-google w-5 h-5 text-red-500"></i>
                        Test Button - Click Me
                    </button>
                    
                    <div id="testResult" class="hidden p-4 bg-green-100 border border-green-200 rounded-lg">
                        <p class="text-green-800">✅ Interface is working correctly!</p>
                        <p class="text-sm text-green-600 mt-2">All dependencies loaded successfully.</p>
                    </div>
                </div>
            </div>

            <div class="mt-8 grid grid-cols-3 gap-4 text-center">
                <div class="glass rounded-xl p-4">
                    <div class="w-8 h-8 red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
                        <i data-lucide="music" class="w-4 h-4 text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">Tailwind CSS</p>
                </div>
                <div class="glass rounded-xl p-4">
                    <div class="w-8 h-8 red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
                        <i data-lucide="headphones" class="w-4 h-4 text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">Lucide Icons</p>
                </div>
                <div class="glass rounded-xl p-4">
                    <div class="w-8 h-8 red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
                        <i class="fab fa-google w-4 h-4 text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">Font Awesome</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testFunction() {
            document.getElementById('testResult').classList.remove('hidden');
            alert('✅ MP3 World interface is working correctly!\n\nAll dependencies are loaded:\n• Tailwind CSS\n• Lucide Icons\n• Font Awesome\n\nYou can now use the main interface.');
        }

        window.onload = function() {
            lucide.createIcons();
            console.log('MP3 World Test Page Loaded Successfully');
        };
    </script>
</body>
</html>
