'use client'

import { useState } from 'react'
import { 
  <PERSON>, 
  Pause, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  Volume2, 
  <PERSON>, 
  Shuffle, 
  Repeat,
  Maximize2
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/Button'

interface Track {
  id: string
  title: string
  artist: string
  album: string
  duration: string
  image: string
}

interface PlayerProps {
  currentTrack: Track | null
  isPlaying: boolean
  onPlayPause: () => void
}

export function Player({ currentTrack, isPlaying, onPlayPause }: PlayerProps) {
  const [volume, setVolume] = useState(75)
  const [progress, setProgress] = useState(30)
  const [isLiked, setIsLiked] = useState(false)
  const [isShuffled, setIsShuffled] = useState(false)
  const [repeatMode, setRepeatMode] = useState(0) // 0: off, 1: all, 2: one

  if (!currentTrack) {
    return (
      <div className="h-20 glass border-t border-white/20 flex items-center justify-center">
        <p className="text-gray-500">Select a song to start playing</p>
      </div>
    )
  }

  return (
    <div className="h-20 glass border-t border-white/20 flex items-center justify-between px-4">
      {/* Current Track Info */}
      <div className="flex items-center gap-3 w-1/4">
        <img
          src={currentTrack.image}
          alt={currentTrack.title}
          className="w-14 h-14 rounded-lg"
        />
        <div className="min-w-0">
          <p className="font-medium text-gray-900 truncate">{currentTrack.title}</p>
          <p className="text-sm text-gray-600 truncate">{currentTrack.artist}</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsLiked(!isLiked)}
          className={`p-2 ${isLiked ? 'text-red-primary' : 'text-gray-400'}`}
        >
          <Heart className={`w-4 h-4 ${isLiked ? 'fill-current' : ''}`} />
        </Button>
      </div>

      {/* Player Controls */}
      <div className="flex flex-col items-center w-1/2 max-w-md">
        {/* Control Buttons */}
        <div className="flex items-center gap-2 mb-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsShuffled(!isShuffled)}
            className={`p-2 ${isShuffled ? 'text-red-primary' : 'text-gray-400'}`}
          >
            <Shuffle className="w-4 h-4" />
          </Button>
          
          <Button variant="ghost" size="sm" className="p-2 text-gray-700">
            <SkipBack className="w-5 h-5" />
          </Button>
          
          <Button
            onClick={onPlayPause}
            className="w-10 h-10 rounded-full bg-red-primary hover:bg-red-dark text-white flex items-center justify-center"
          >
            {isPlaying ? (
              <Pause className="w-5 h-5" />
            ) : (
              <Play className="w-5 h-5 ml-0.5" />
            )}
          </Button>
          
          <Button variant="ghost" size="sm" className="p-2 text-gray-700">
            <SkipForward className="w-5 h-5" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setRepeatMode((prev) => (prev + 1) % 3)}
            className={`p-2 ${repeatMode > 0 ? 'text-red-primary' : 'text-gray-400'}`}
          >
            <Repeat className="w-4 h-4" />
            {repeatMode === 2 && (
              <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-primary rounded-full"></span>
            )}
          </Button>
        </div>

        {/* Progress Bar */}
        <div className="flex items-center gap-2 w-full">
          <span className="text-xs text-gray-500">1:23</span>
          <div className="flex-1 h-1 bg-gray-300 rounded-full overflow-hidden">
            <div 
              className="h-full bg-red-primary transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <span className="text-xs text-gray-500">{currentTrack.duration}</span>
        </div>
      </div>

      {/* Volume and Additional Controls */}
      <div className="flex items-center gap-2 w-1/4 justify-end">
        <Button variant="ghost" size="sm" className="p-2 text-gray-400">
          <Maximize2 className="w-4 h-4" />
        </Button>
        
        <div className="flex items-center gap-2">
          <Volume2 className="w-4 h-4 text-gray-400" />
          <div className="w-20 h-1 bg-gray-300 rounded-full overflow-hidden">
            <div 
              className="h-full bg-red-primary transition-all duration-300"
              style={{ width: `${volume}%` }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
