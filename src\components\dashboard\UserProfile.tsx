'use client'

import { useSession } from 'next-auth/react'
import { User, Mail, Shield, Calendar, ExternalLink } from 'lucide-react'
import { Button } from '@/components/ui/Button'

export function UserProfile() {
  const { data: session } = useSession()

  if (!session?.user) {
    return null
  }

  const { user } = session

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="glass rounded-2xl p-8 shadow-xl">
        {/* Header */}
        <div className="flex items-center gap-6 mb-8">
          {user.image ? (
            <img
              src={user.image}
              alt={user.name || 'User'}
              className="w-20 h-20 rounded-full border-4 border-white shadow-lg"
            />
          ) : (
            <div className="w-20 h-20 bg-red-gradient rounded-full flex items-center justify-center border-4 border-white shadow-lg">
              <User className="w-10 h-10 text-white" />
            </div>
          )}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{user.name || 'Unknown User'}</h1>
            <p className="text-gray-600">{user.email}</p>
            {user.provider && (
              <div className="flex items-center gap-2 mt-2">
                <span className="px-3 py-1 bg-red-100 text-red-700 rounded-full text-sm font-medium capitalize">
                  {user.provider} Account
                </span>
                {user.verified_email && (
                  <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium flex items-center gap-1">
                    <Shield className="w-3 h-3" />
                    Verified
                  </span>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Account Details */}
        <div className="space-y-6">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Account Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white/30 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <User className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-600">User ID</span>
                </div>
                <p className="text-gray-900 font-mono text-sm">{user.id}</p>
              </div>

              <div className="bg-white/30 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Mail className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-600">Email</span>
                </div>
                <p className="text-gray-900">{user.email}</p>
              </div>

              {user.googleId && (
                <div className="bg-white/30 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <ExternalLink className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium text-gray-600">Google ID</span>
                  </div>
                  <p className="text-gray-900 font-mono text-sm">{user.googleId}</p>
                </div>
              )}

              <div className="bg-white/30 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Calendar className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-600">Login Method</span>
                </div>
                <p className="text-gray-900 capitalize">{user.provider || 'Unknown'}</p>
              </div>
            </div>
          </div>

          {/* Google-specific information */}
          {user.provider === 'google' && (
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Google Account Details</h2>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <ExternalLink className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-blue-900">Connected to Google</h3>
                    <p className="text-sm text-blue-700 mt-1">
                      Your account is securely connected to Google. We have access to your basic profile information including:
                    </p>
                    <ul className="text-sm text-blue-700 mt-2 space-y-1">
                      <li>• Name and profile picture</li>
                      <li>• Email address {user.verified_email && '(verified)'}</li>
                      <li>• Google account ID</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-4 pt-4 border-t border-white/20">
            <Button variant="outline" className="flex-1">
              Edit Profile
            </Button>
            <Button variant="secondary" className="flex-1">
              Privacy Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
