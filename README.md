# Spotify Clone - Music Streaming App

A beautiful Spotify-like music streaming interface with authentication using Apple, Google, GitHub, and Discord providers. Features a stunning light reddish theme instead of the traditional green.

## Features

- 🎵 **Beautiful UI**: Spotify-inspired interface with light reddish theme
- 🔐 **Multi-Provider Authentication**: Login with Google, Apple, GitHub, Discord
- 🎨 **Glass Morphism Design**: Modern glass effects and smooth animations
- 📱 **Responsive Design**: Works perfectly on desktop and mobile
- 🎧 **Music Player**: Full-featured player with controls and progress tracking
- 📚 **Library Management**: Playlists, liked songs, and recently played
- 🔍 **Search Functionality**: Search for songs, artists, and albums

## Tech Stack

- **Framework**: Next.js 14 with App Router
- **Authentication**: NextAuth.js
- **Styling**: Tailwind CSS with custom red theme
- **Icons**: Lucide React
- **TypeScript**: Full type safety

## Setup Instructions

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Environment Variables**:
   Copy `.env.local` and fill in your OAuth provider credentials:
   ```bash
   # Required for NextAuth
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-secret-key-here

   # OAuth Providers (get these from respective developer consoles)
   GOOGLE_CLIENT_ID=your-google-client-id
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   
   GITHUB_ID=your-github-client-id
   GITHUB_SECRET=your-github-client-secret
   
   DISCORD_CLIENT_ID=your-discord-client-id
   DISCORD_CLIENT_SECRET=your-discord-client-secret
   
   APPLE_ID=your-apple-client-id
   APPLE_SECRET=your-apple-client-secret
   ```

3. **Run Development Server**:
   ```bash
   npm run dev
   ```

4. **Open Browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## OAuth Setup

### Google OAuth
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add `http://localhost:3000/api/auth/callback/google` to authorized redirect URIs

### GitHub OAuth
1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create a new OAuth App
3. Set Authorization callback URL to `http://localhost:3000/api/auth/callback/github`

### Discord OAuth
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application
3. Go to OAuth2 section
4. Add redirect URL: `http://localhost:3000/api/auth/callback/discord`

### Apple OAuth
1. Go to [Apple Developer Console](https://developer.apple.com/)
2. Create a new App ID and Service ID
3. Configure Sign in with Apple
4. Add redirect URL: `http://localhost:3000/api/auth/callback/apple`

## Design Features

- **Light Reddish Theme**: Beautiful gradient from light pink to coral red
- **Glass Morphism**: Translucent elements with backdrop blur effects
- **Smooth Animations**: Hover effects and transitions throughout
- **Responsive Layout**: Adapts to all screen sizes
- **Modern Typography**: Clean, readable fonts with proper hierarchy

## Project Structure

```
src/
├── app/
│   ├── api/auth/[...nextauth]/route.ts
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── auth/
│   │   ├── AuthProvider.tsx
│   │   └── LoginPage.tsx
│   ├── dashboard/
│   │   ├── Dashboard.tsx
│   │   ├── MainContent.tsx
│   │   ├── Player.tsx
│   │   ├── Sidebar.tsx
│   │   └── TopBar.tsx
│   └── ui/
│       ├── Button.tsx
│       └── LoadingSpinner.tsx
└── lib/
    └── utils.ts
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - feel free to use this project for your own purposes.
