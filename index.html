<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MP3 World - Music Streaming</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <!-- Font Awesome for brand icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #FFF5F5 0%, #FED7D7 50%, #FEB2B2 100%);
            min-height: 100vh;
        }
        
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .red-gradient {
            background: linear-gradient(135deg, #E53E3E 0%, #FC8181 100%);
        }
        
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(229, 62, 62, 0.6);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: rgba(229, 62, 62, 0.8);
        }
        
        * {
            transition: all 0.2s ease-in-out;
        }
    </style>
</head>
<body>
    <!-- Login Page (Initially shown) -->
    <div id="loginPage" class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            <!-- Logo and Header -->
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 red-gradient rounded-full mb-4 shadow-lg">
                    <i data-lucide="music" class="w-8 h-8 text-white"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    Welcome to MP3 World
                </h1>
                <p class="text-gray-600">
                    Sign in to access your music library
                </p>
            </div>

            <!-- Login Card -->
            <div class="glass rounded-2xl p-8 shadow-xl">
                <div class="space-y-4">
                    <button onclick="authenticateWithGoogle()" class="w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 shadow-md">
                        <i class="fab fa-google w-5 h-5 text-red-500"></i>
                        Continue with Google
                    </button>

                    <button onclick="showCustomAuth()" class="w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 bg-red-600 hover:bg-red-700 text-white shadow-md">
                        <i data-lucide="edit" class="w-5 h-5"></i>
                        Custom Authentication
                    </button>

                    <button onclick="showDashboard()" class="w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 bg-black hover:bg-gray-900 text-white shadow-md">
                        <i class="fab fa-apple w-5 h-5"></i>
                        Continue with Apple
                    </button>

                    <button onclick="showDashboard()" class="w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 bg-gray-900 hover:bg-gray-800 text-white shadow-md">
                        <i class="fab fa-github w-5 h-5"></i>
                        Continue with GitHub
                    </button>

                    <button onclick="showDashboard()" class="w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 bg-indigo-600 hover:bg-indigo-700 text-white shadow-md">
                        <i class="fab fa-discord w-5 h-5"></i>
                        Continue with Discord
                    </button>
                </div>

                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-500">
                        By signing in, you agree to our 
                        <a href="#" class="text-red-600 hover:text-red-800 underline">Terms of Service</a> 
                        and 
                        <a href="#" class="text-red-600 hover:text-red-800 underline">Privacy Policy</a>
                    </p>
                </div>
            </div>

            <!-- Features -->
            <div class="mt-8 grid grid-cols-3 gap-4 text-center">
                <div class="glass rounded-xl p-4">
                    <div class="w-8 h-8 red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
                        <i data-lucide="music" class="w-4 h-4 text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">Unlimited Music</p>
                </div>
                <div class="glass rounded-xl p-4">
                    <div class="w-8 h-8 red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
                        <i data-lucide="headphones" class="w-4 h-4 text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">High Quality</p>
                </div>
                <div class="glass rounded-xl p-4">
                    <div class="w-8 h-8 red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
                        <i data-lucide="download" class="w-4 h-4 text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">Offline Mode</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Authentication Modal (Initially hidden) -->
    <div id="customAuthModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 hidden z-50">
        <div class="w-full max-w-md glass rounded-2xl p-8 shadow-xl">
            <div class="text-center mb-6">
                <div class="inline-flex items-center justify-center w-12 h-12 red-gradient rounded-full mb-4">
                    <i data-lucide="edit" class="w-6 h-6 text-white"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Custom Authentication</h2>
                <p class="text-gray-600">Enter your custom ID to authenticate</p>
            </div>

            <form onsubmit="authenticateWithCustomId(event)" class="space-y-4">
                <div>
                    <label for="customUserId" class="block text-sm font-medium text-gray-700 mb-2">
                        User ID
                    </label>
                    <input
                        type="text"
                        id="customUserId"
                        placeholder="Enter your custom user ID"
                        class="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                        required
                    />
                </div>

                <div>
                    <label for="customUserName" class="block text-sm font-medium text-gray-700 mb-2">
                        Display Name
                    </label>
                    <input
                        type="text"
                        id="customUserName"
                        placeholder="Enter your display name"
                        class="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                        required
                    />
                </div>

                <div>
                    <label for="customUserEmail" class="block text-sm font-medium text-gray-700 mb-2">
                        Email Address
                    </label>
                    <input
                        type="email"
                        id="customUserEmail"
                        placeholder="Enter your email address"
                        class="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                        required
                    />
                </div>

                <div>
                    <label for="customUserImage" class="block text-sm font-medium text-gray-700 mb-2">
                        Profile Image URL (Optional)
                    </label>
                    <input
                        type="url"
                        id="customUserImage"
                        placeholder="https://example.com/your-image.jpg"
                        class="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                    />
                    <p class="text-xs text-gray-500 mt-1">Leave empty to use default avatar</p>
                </div>

                <div class="flex gap-3 pt-4">
                    <button
                        type="button"
                        onclick="hideCustomAuth()"
                        class="flex-1 py-3 px-4 rounded-xl font-medium bg-gray-200 hover:bg-gray-300 text-gray-700 transition-colors"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        class="flex-1 py-3 px-4 rounded-xl font-medium red-gradient text-white hover:opacity-90 transition-opacity"
                    >
                        Authenticate
                    </button>
                </div>
            </form>

            <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="text-sm font-medium text-blue-900 mb-2">💡 Pro Tip</h4>
                <p class="text-xs text-blue-700">
                    You can use any image URL for your profile picture. Try using:
                    <br>• Your social media profile image
                    <br>• A Gravatar URL
                    <br>• Any public image URL
                </p>
            </div>
        </div>
    </div>

    <!-- Dashboard (Initially hidden) -->
    <div id="dashboard" class="h-screen flex flex-col hidden">
        <!-- Top Bar -->
        <div class="h-16 glass border-b border-white/20 flex items-center justify-between px-6">
            <!-- Search Bar -->
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                    <input
                        type="text"
                        placeholder="Search for songs, artists, or albums..."
                        class="w-full pl-10 pr-4 py-2 bg-white/20 border border-white/30 rounded-full text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                    />
                </div>
            </div>

            <!-- User Menu -->
            <div class="flex items-center gap-4">
                <button class="p-2 hover:bg-white/20 rounded-full">
                    <i data-lucide="bell" class="w-5 h-5 text-gray-600"></i>
                </button>
                
                <div class="flex items-center gap-2 p-2 rounded-full hover:bg-white/20">
                    <img id="userAvatar" src="" alt="User Avatar" class="w-8 h-8 rounded-full hidden">
                    <div id="defaultAvatar" class="w-8 h-8 red-gradient rounded-full flex items-center justify-center">
                        <i data-lucide="user" class="w-4 h-4 text-white"></i>
                    </div>
                    <div class="flex flex-col">
                        <span id="userName" class="text-sm font-medium text-gray-700">Demo User</span>
                        <span id="userProvider" class="text-xs text-green-600 hidden">✓ via Google</span>
                    </div>
                    <button onclick="showLogin()" class="text-xs text-red-600 hover:text-red-800 ml-2">Logout</button>
                </div>
            </div>
        </div>
        
        <!-- Main Layout -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Sidebar -->
            <div class="w-64 glass border-r border-white/20 flex flex-col">
                <!-- Logo -->
                <div class="p-6 border-b border-white/20">
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 red-gradient rounded-lg flex items-center justify-center">
                            <i data-lucide="music" class="w-5 h-5 text-white"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-800">MP3 World</span>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="flex-1 overflow-y-auto p-4 space-y-6">
                    <div class="space-y-2">
                        <button class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left bg-red-600 text-white">
                            <i data-lucide="home" class="w-5 h-5"></i>
                            <span class="font-medium">Home</span>
                        </button>
                        <button class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-700 hover:bg-white/30">
                            <i data-lucide="search" class="w-5 h-5"></i>
                            <span class="font-medium">Search</span>
                        </button>
                        <button class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-700 hover:bg-white/30">
                            <i data-lucide="library" class="w-5 h-5"></i>
                            <span class="font-medium">Your Library</span>
                        </button>
                    </div>

                    <div>
                        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Library</h3>
                        <div class="space-y-1">
                            <button class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-600 hover:bg-white/30">
                                <i data-lucide="plus" class="w-4 h-4"></i>
                                <span class="text-sm">Create Playlist</span>
                            </button>
                            <button class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-600 hover:bg-white/30">
                                <i data-lucide="heart" class="w-4 h-4"></i>
                                <span class="text-sm">Liked Songs</span>
                            </button>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Playlists</h3>
                        <div class="space-y-1">
                            <button class="w-full text-left px-3 py-2 rounded-lg text-gray-600 hover:bg-white/30">
                                <span class="text-sm">My Playlist #1</span>
                            </button>
                            <button class="w-full text-left px-3 py-2 rounded-lg text-gray-600 hover:bg-white/30">
                                <span class="text-sm">Chill Vibes</span>
                            </button>
                            <button class="w-full text-left px-3 py-2 rounded-lg text-gray-600 hover:bg-white/30">
                                <span class="text-sm">Workout Mix</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="flex-1 overflow-y-auto p-8">
                <!-- Authentication Status -->
                <div id="authStatus" class="glass rounded-lg p-4 mb-6 hidden">
                    <div class="flex items-center gap-2 mb-3">
                        <i data-lucide="check-circle" class="w-5 h-5 text-green-500"></i>
                        <span class="text-sm font-medium text-gray-900">Google Authentication Successful</span>
                    </div>
                    <div class="grid grid-cols-2 gap-4 text-xs">
                        <div>
                            <span class="font-medium text-gray-600">Provider:</span>
                            <span class="ml-2 text-gray-900">Google</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-600">Email Verified:</span>
                            <span class="ml-2 text-gray-900">✅ Yes</span>
                        </div>
                        <div class="col-span-2">
                            <span class="font-medium text-gray-600">Google ID:</span>
                            <span id="displayGoogleId" class="ml-2 text-gray-900 font-mono">1234567890123456789</span>
                        </div>
                    </div>
                </div>

                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">
                        Good afternoon<span id="welcomeName"></span>
                    </h1>
                    <p class="text-gray-600">Ready to discover your next favorite song?</p>
                    <div id="googleConnected" class="flex items-center gap-2 mt-2 hidden">
                        <span class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                            ✓ Connected via Google
                        </span>
                    </div>
                </div>

                <!-- Featured Playlists -->
                <section class="mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Featured Playlists</h2>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="glass rounded-xl p-4 hover:bg-white/40 transition-all duration-200 cursor-pointer group">
                            <div class="relative mb-4">
                                <div class="w-full aspect-square bg-gradient-to-br from-red-400 to-red-600 rounded-lg flex items-center justify-center">
                                    <i data-lucide="music" class="w-12 h-12 text-white"></i>
                                </div>
                                <button class="absolute bottom-2 right-2 w-12 h-12 red-gradient rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:scale-105">
                                    <i data-lucide="play" class="w-5 h-5 ml-1"></i>
                                </button>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-1">Today's Top Hits</h3>
                            <p class="text-sm text-gray-600">The most played songs right now</p>
                        </div>
                        
                        <div class="glass rounded-xl p-4 hover:bg-white/40 transition-all duration-200 cursor-pointer group">
                            <div class="relative mb-4">
                                <div class="w-full aspect-square bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center">
                                    <i data-lucide="headphones" class="w-12 h-12 text-white"></i>
                                </div>
                                <button class="absolute bottom-2 right-2 w-12 h-12 red-gradient rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:scale-105">
                                    <i data-lucide="play" class="w-5 h-5 ml-1"></i>
                                </button>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-1">Chill Vibes</h3>
                            <p class="text-sm text-gray-600">Relax and unwind with these tracks</p>
                        </div>
                        
                        <div class="glass rounded-xl p-4 hover:bg-white/40 transition-all duration-200 cursor-pointer group">
                            <div class="relative mb-4">
                                <div class="w-full aspect-square bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center">
                                    <i data-lucide="zap" class="w-12 h-12 text-white"></i>
                                </div>
                                <button class="absolute bottom-2 right-2 w-12 h-12 red-gradient rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:scale-105">
                                    <i data-lucide="play" class="w-5 h-5 ml-1"></i>
                                </button>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-1">Workout Mix</h3>
                            <p class="text-sm text-gray-600">High energy songs for your workout</p>
                        </div>
                        
                        <div class="glass rounded-xl p-4 hover:bg-white/40 transition-all duration-200 cursor-pointer group">
                            <div class="relative mb-4">
                                <div class="w-full aspect-square bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center">
                                    <i data-lucide="radio" class="w-12 h-12 text-white"></i>
                                </div>
                                <button class="absolute bottom-2 right-2 w-12 h-12 red-gradient rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:scale-105">
                                    <i data-lucide="play" class="w-5 h-5 ml-1"></i>
                                </button>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-1">Jazz Classics</h3>
                            <p class="text-sm text-gray-600">Timeless jazz standards</p>
                        </div>
                    </div>
                </section>

                <!-- Recently Played -->
                <section>
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Recently Played</h2>
                    <div class="glass rounded-xl overflow-hidden">
                        <div class="p-4 border-b border-white/20">
                            <div class="grid grid-cols-12 gap-4 text-sm font-medium text-gray-600 uppercase tracking-wider">
                                <div class="col-span-1">#</div>
                                <div class="col-span-5">Title</div>
                                <div class="col-span-3">Album</div>
                                <div class="col-span-2">Duration</div>
                                <div class="col-span-1"></div>
                            </div>
                        </div>
                        <div class="divide-y divide-white/10">
                            <div class="p-4 hover:bg-white/20 transition-colors cursor-pointer group">
                                <div class="grid grid-cols-12 gap-4 items-center">
                                    <div class="col-span-1">
                                        <span class="text-gray-600 group-hover:hidden">1</span>
                                        <i data-lucide="play" class="w-4 h-4 text-red-600 hidden group-hover:block"></i>
                                    </div>
                                    <div class="col-span-5 flex items-center gap-3">
                                        <div class="w-10 h-10 red-gradient rounded flex items-center justify-center">
                                            <i data-lucide="music" class="w-5 h-5 text-white"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">Blinding Lights</p>
                                            <p class="text-sm text-gray-600">The Weeknd</p>
                                        </div>
                                    </div>
                                    <div class="col-span-3">
                                        <p class="text-gray-600">After Hours</p>
                                    </div>
                                    <div class="col-span-2">
                                        <p class="text-gray-600">3:20</p>
                                    </div>
                                    <div class="col-span-1 flex items-center gap-2">
                                        <button class="p-1 opacity-0 group-hover:opacity-100 hover:text-red-600">
                                            <i data-lucide="heart" class="w-4 h-4"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="p-4 hover:bg-white/20 transition-colors cursor-pointer group">
                                <div class="grid grid-cols-12 gap-4 items-center">
                                    <div class="col-span-1">
                                        <span class="text-gray-600 group-hover:hidden">2</span>
                                        <i data-lucide="play" class="w-4 h-4 text-red-600 hidden group-hover:block"></i>
                                    </div>
                                    <div class="col-span-5 flex items-center gap-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded flex items-center justify-center">
                                            <i data-lucide="music" class="w-5 h-5 text-white"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">Watermelon Sugar</p>
                                            <p class="text-sm text-gray-600">Harry Styles</p>
                                        </div>
                                    </div>
                                    <div class="col-span-3">
                                        <p class="text-gray-600">Fine Line</p>
                                    </div>
                                    <div class="col-span-2">
                                        <p class="text-gray-600">2:54</p>
                                    </div>
                                    <div class="col-span-1 flex items-center gap-2">
                                        <button class="p-1 opacity-0 group-hover:opacity-100 hover:text-red-600">
                                            <i data-lucide="heart" class="w-4 h-4"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
        
        <!-- Bottom Player -->
        <div class="h-20 glass border-t border-white/20 flex items-center justify-between px-4">
            <!-- Current Track Info -->
            <div class="flex items-center gap-3 w-1/4">
                <div class="w-14 h-14 red-gradient rounded-lg flex items-center justify-center">
                    <i data-lucide="music" class="w-6 h-6 text-white"></i>
                </div>
                <div class="min-w-0">
                    <p class="font-medium text-gray-900 truncate">Blinding Lights</p>
                    <p class="text-sm text-gray-600 truncate">The Weeknd</p>
                </div>
                <button class="p-2 text-red-600">
                    <i data-lucide="heart" class="w-4 h-4 fill-current"></i>
                </button>
            </div>

            <!-- Player Controls -->
            <div class="flex flex-col items-center w-1/2 max-w-md">
                <!-- Control Buttons -->
                <div class="flex items-center gap-2 mb-2">
                    <button class="p-2 text-gray-400 hover:text-red-600">
                        <i data-lucide="shuffle" class="w-4 h-4"></i>
                    </button>
                    
                    <button class="p-2 text-gray-700 hover:text-red-600">
                        <i data-lucide="skip-back" class="w-5 h-5"></i>
                    </button>
                    
                    <button class="w-10 h-10 rounded-full red-gradient text-white flex items-center justify-center hover:scale-105">
                        <i data-lucide="pause" class="w-5 h-5"></i>
                    </button>
                    
                    <button class="p-2 text-gray-700 hover:text-red-600">
                        <i data-lucide="skip-forward" class="w-5 h-5"></i>
                    </button>
                    
                    <button class="p-2 text-gray-400 hover:text-red-600">
                        <i data-lucide="repeat" class="w-4 h-4"></i>
                    </button>
                </div>

                <!-- Progress Bar -->
                <div class="flex items-center gap-2 w-full">
                    <span class="text-xs text-gray-500">1:23</span>
                    <div class="flex-1 h-1 bg-gray-300 rounded-full overflow-hidden">
                        <div class="h-full bg-red-600 transition-all duration-300" style="width: 30%"></div>
                    </div>
                    <span class="text-xs text-gray-500">3:20</span>
                </div>
            </div>

            <!-- Volume and Additional Controls -->
            <div class="flex items-center gap-2 w-1/4 justify-end">
                <div class="flex items-center gap-2">
                    <i data-lucide="volume-2" class="w-4 h-4 text-gray-400"></i>
                    <div class="w-20 h-1 bg-gray-300 rounded-full overflow-hidden">
                        <div class="h-full bg-red-600 transition-all duration-300" style="width: 75%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Simulated Google user data
        const mockGoogleUser = {
            id: "google_" + Math.random().toString(36).substr(2, 9),
            googleId: "1234567890123456789",
            name: "John Doe",
            email: "<EMAIL>",
            picture: "https://lh3.googleusercontent.com/a/default-user=s96-c",
            verified_email: true,
            provider: "google"
        };

        function authenticateWithGoogle() {
            // Simulate Google OAuth flow
            const authWindow = window.open('', '_blank', 'width=500,height=600');
            authWindow.document.write(`
                <html>
                    <head><title>Google Sign In</title></head>
                    <body style="font-family: Arial, sans-serif; padding: 20px; text-align: center;">
                        <h2>🔐 Google Authentication</h2>
                        <p>Simulating Google OAuth flow...</p>
                        <div style="margin: 20px 0;">
                            <div style="border: 1px solid #ddd; padding: 15px; border-radius: 8px; background: #f9f9f9;">
                                <h3>Account Information</h3>
                                <p><strong>Name:</strong> ${mockGoogleUser.name}</p>
                                <p><strong>Email:</strong> ${mockGoogleUser.email}</p>
                                <p><strong>Google ID:</strong> ${mockGoogleUser.googleId}</p>
                                <p><strong>Verified:</strong> ✅ Yes</p>
                            </div>
                        </div>
                        <button onclick="window.close(); window.opener.completeGoogleAuth();"
                                style="background: #4285f4; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                            Continue to MP3 World
                        </button>
                    </body>
                </html>
            `);
        }

        function completeGoogleAuth() {
            // Store user data
            localStorage.setItem('mp3_world_user', JSON.stringify(mockGoogleUser));

            // Update UI with Google user info
            updateUserInterface(mockGoogleUser);

            showDashboard();

            // Show success message
            setTimeout(() => {
                alert(`✅ Successfully authenticated with Google!\n\nUser: ${mockGoogleUser.name}\nEmail: ${mockGoogleUser.email}\nGoogle ID: ${mockGoogleUser.googleId}\nVerified: ${mockGoogleUser.verified_email ? 'Yes' : 'No'}`);
            }, 500);
        }

        function updateUserInterface(user) {
            // Update top bar user info
            document.getElementById('userName').textContent = user.name;
            document.getElementById('userProvider').textContent = '✓ via ' + user.provider;
            document.getElementById('userProvider').classList.remove('hidden');

            // Show user avatar if available
            if (user.picture) {
                document.getElementById('userAvatar').src = user.picture;
                document.getElementById('userAvatar').classList.remove('hidden');
                document.getElementById('defaultAvatar').classList.add('hidden');
            }

            // Update welcome message
            const firstName = user.name.split(' ')[0];
            document.getElementById('welcomeName').textContent = ', ' + firstName;

            // Show authentication status
            document.getElementById('authStatus').classList.remove('hidden');
            document.getElementById('displayGoogleId').textContent = user.googleId;
            document.getElementById('googleConnected').classList.remove('hidden');
        }

        function showDashboard() {
            document.getElementById('loginPage').classList.add('hidden');
            document.getElementById('dashboard').classList.remove('hidden');
            // Re-initialize icons for the new content
            lucide.createIcons();
        }

        function showLogin() {
            // Clear stored user data
            localStorage.removeItem('mp3_world_user');

            // Reset UI
            document.getElementById('userName').textContent = 'Demo User';
            document.getElementById('userProvider').classList.add('hidden');
            document.getElementById('userAvatar').classList.add('hidden');
            document.getElementById('defaultAvatar').classList.remove('hidden');
            document.getElementById('welcomeName').textContent = '';
            document.getElementById('authStatus').classList.add('hidden');
            document.getElementById('googleConnected').classList.add('hidden');

            document.getElementById('dashboard').classList.add('hidden');
            document.getElementById('loginPage').classList.remove('hidden');
            // Re-initialize icons for the new content
            lucide.createIcons();
        }

        // Check if user is already logged in
        window.onload = function() {
            const storedUser = localStorage.getItem('mp3_world_user');
            if (storedUser) {
                const user = JSON.parse(storedUser);
                updateUserInterface(user);
                showDashboard();
            }
        };
    </script>
</body>
</html>
