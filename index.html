<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MP3 World - Music Streaming</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <!-- Font Awesome for brand icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #FFF5F5 0%, #FED7D7 50%, #FEB2B2 100%);
            min-height: 100vh;
        }
        
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .red-gradient {
            background: linear-gradient(135deg, #E53E3E 0%, #FC8181 100%);
        }
        
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(229, 62, 62, 0.6);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: rgba(229, 62, 62, 0.8);
        }
        
        * {
            transition: all 0.2s ease-in-out;
        }

        .track-playing {
            background: rgba(229, 62, 62, 0.1);
            border-left: 3px solid #E53E3E;
        }

        .track-playing .track-number {
            color: #E53E3E !important;
        }

        .audio-visualizer {
            display: inline-flex;
            align-items: center;
            gap: 2px;
        }

        .audio-visualizer span {
            width: 2px;
            height: 12px;
            background: #E53E3E;
            animation: audioWave 1s ease-in-out infinite alternate;
        }

        .audio-visualizer span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .audio-visualizer span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes audioWave {
            0% { height: 4px; }
            100% { height: 12px; }
        }
    </style>
</head>
<body>
    <!-- Login Page (Initially shown) -->
    <div id="loginPage" class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            <!-- Logo and Header -->
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 red-gradient rounded-full mb-4 shadow-lg">
                    <i data-lucide="music" class="w-8 h-8 text-white"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    Welcome to MP3 World
                </h1>
                <p class="text-gray-600">
                    Sign in to access your music library
                </p>
            </div>

            <!-- Login Card -->
            <div class="glass rounded-2xl p-8 shadow-xl">
                <!-- Terms Notice -->
                <div class="text-xs text-gray-600 mb-6 text-center">
                    By continuing, you agree to our <a href="#" class="text-blue-600 underline">Terms of Use</a> and acknowledge our <a href="#" class="text-blue-600 underline">Privacy Policy</a>.
                </div>

                <!-- Checkbox -->
                <div class="flex items-start gap-3 mb-6">
                    <input type="checkbox" id="marketingEmails" class="mt-1 w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500">
                    <label for="marketingEmails" class="text-sm text-gray-600">
                        Click here if you do not want to receive marketing emails from MP3 World and affiliates
                    </label>
                </div>

                <div class="space-y-4">
                    <!-- Google Sign In Button -->
                    <button onclick="authenticateWithGoogle()" class="w-full flex items-center justify-center gap-3 py-4 px-6 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 shadow-sm">
                        <svg width="20" height="20" viewBox="0 0 24 24">
                            <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Continue with Google
                    </button>

                    <!-- Apple Sign In Button -->
                    <button onclick="authenticateWithApple()" class="w-full flex items-center justify-center gap-3 py-4 px-6 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 bg-black hover:bg-gray-800 text-white shadow-sm">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                        </svg>
                        Continue with Apple
                    </button>

                    <!-- Divider -->
                    <div class="relative my-6">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">or</span>
                        </div>
                    </div>

                    <!-- Email Input -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Enter email</label>
                        <input type="email" id="emailInput" placeholder="Enter your email address" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent">
                    </div>

                    <!-- Continue with Email Button -->
                    <button onclick="authenticateWithEmail()" class="w-full py-4 px-6 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 bg-red-600 hover:bg-red-700 text-white shadow-sm">
                        Continue with email
                    </button>

                    <!-- Custom ID Authentication Form -->
                    <div class="border-t border-white/30 pt-4 mt-4">
                        <h3 class="text-sm font-medium text-gray-700 mb-3 text-center">Or enter your custom ID</h3>
                        <form onsubmit="authenticateWithCustomId(event)" class="space-y-3">
                            <input
                                type="text"
                                id="customUserId"
                                placeholder="Enter your custom user ID"
                                class="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                                required
                            />
                            <input
                                type="text"
                                id="customUserName"
                                placeholder="Enter your display name"
                                class="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                                required
                            />
                            <input
                                type="email"
                                id="customUserEmail"
                                placeholder="Enter your email address"
                                class="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                                required
                            />
                            <input
                                type="url"
                                id="customUserImage"
                                placeholder="Profile image URL (optional)"
                                class="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                            />
                            <button
                                type="submit"
                                class="w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 red-gradient text-white shadow-md"
                            >
                                <i data-lucide="user-check" class="w-5 h-5"></i>
                                Login with Custom ID
                            </button>
                        </form>
                    </div>

                    <!-- Other Authentication Options -->
                    <div class="border-t border-white/30 pt-4 mt-4">
                        <h3 class="text-sm font-medium text-gray-700 mb-3 text-center">Or continue with</h3>
                        <div class="space-y-3">
                            <button onclick="showDashboard()" class="w-full flex items-center justify-center gap-3 py-2 px-4 rounded-lg font-medium transition-all duration-200 bg-black hover:bg-gray-900 text-white text-sm">
                                <i class="fab fa-apple w-4 h-4"></i>
                                Apple
                            </button>

                            <button onclick="showDashboard()" class="w-full flex items-center justify-center gap-3 py-2 px-4 rounded-lg font-medium transition-all duration-200 bg-gray-900 hover:bg-gray-800 text-white text-sm">
                                <i class="fab fa-github w-4 h-4"></i>
                                GitHub
                            </button>

                            <button onclick="showDashboard()" class="w-full flex items-center justify-center gap-3 py-2 px-4 rounded-lg font-medium transition-all duration-200 bg-indigo-600 hover:bg-indigo-700 text-white text-sm">
                                <i class="fab fa-discord w-4 h-4"></i>
                                Discord
                            </button>
                        </div>
                    </div>
                </div>

                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-500">
                        By signing in, you agree to our
                        <a href="#" class="text-red-600 hover:text-red-800 underline">Terms of Service</a>
                        and
                        <a href="#" class="text-red-600 hover:text-red-800 underline">Privacy Policy</a>
                    </p>
                </div>

                <!-- Quick Tips for Custom Authentication -->
                <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 class="text-sm font-medium text-blue-900 mb-1">💡 Custom Authentication Tips</h4>
                    <ul class="text-xs text-blue-700 space-y-1">
                        <li>• Use any unique ID (e.g., username, employee ID)</li>
                        <li>• Profile images can be any public URL</li>
                        <li>• Leave image blank for auto-generated avatar</li>
                    </ul>
                </div>
            </div>

            <!-- Features -->
            <div class="mt-8 grid grid-cols-3 gap-4 text-center">
                <div class="glass rounded-xl p-4">
                    <div class="w-8 h-8 red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
                        <i data-lucide="music" class="w-4 h-4 text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">Unlimited Music</p>
                </div>
                <div class="glass rounded-xl p-4">
                    <div class="w-8 h-8 red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
                        <i data-lucide="headphones" class="w-4 h-4 text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">High Quality</p>
                </div>
                <div class="glass rounded-xl p-4">
                    <div class="w-8 h-8 red-gradient rounded-full mx-auto mb-2 flex items-center justify-center">
                        <i data-lucide="download" class="w-4 h-4 text-white"></i>
                    </div>
                    <p class="text-xs text-gray-600">Offline Mode</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Authentication Modal (Initially hidden) -->
    <div id="customAuthModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 hidden z-50">
        <div class="w-full max-w-md glass rounded-2xl p-8 shadow-xl">
            <div class="text-center mb-6">
                <div class="inline-flex items-center justify-center w-12 h-12 red-gradient rounded-full mb-4">
                    <i data-lucide="edit" class="w-6 h-6 text-white"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Custom Authentication</h2>
                <p class="text-gray-600">Enter your custom ID to authenticate</p>
            </div>

            <form onsubmit="authenticateWithCustomId(event)" class="space-y-4">
                <div>
                    <label for="customUserId" class="block text-sm font-medium text-gray-700 mb-2">
                        User ID
                    </label>
                    <input
                        type="text"
                        id="customUserId"
                        placeholder="Enter your custom user ID"
                        class="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                        required
                    />
                </div>

                <div>
                    <label for="customUserName" class="block text-sm font-medium text-gray-700 mb-2">
                        Display Name
                    </label>
                    <input
                        type="text"
                        id="customUserName"
                        placeholder="Enter your display name"
                        class="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                        required
                    />
                </div>

                <div>
                    <label for="customUserEmail" class="block text-sm font-medium text-gray-700 mb-2">
                        Email Address
                    </label>
                    <input
                        type="email"
                        id="customUserEmail"
                        placeholder="Enter your email address"
                        class="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                        required
                    />
                </div>

                <div>
                    <label for="customUserImage" class="block text-sm font-medium text-gray-700 mb-2">
                        Profile Image URL (Optional)
                    </label>
                    <input
                        type="url"
                        id="customUserImage"
                        placeholder="https://example.com/your-image.jpg"
                        class="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                    />
                    <p class="text-xs text-gray-500 mt-1">Leave empty to use default avatar</p>
                </div>

                <div class="flex gap-3 pt-4">
                    <button
                        type="button"
                        onclick="hideCustomAuth()"
                        class="flex-1 py-3 px-4 rounded-xl font-medium bg-gray-200 hover:bg-gray-300 text-gray-700 transition-colors"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        class="flex-1 py-3 px-4 rounded-xl font-medium red-gradient text-white hover:opacity-90 transition-opacity"
                    >
                        Authenticate
                    </button>
                </div>
            </form>

            <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="text-sm font-medium text-blue-900 mb-2">💡 Pro Tip</h4>
                <p class="text-xs text-blue-700">
                    You can use any image URL for your profile picture. Try using:
                    <br>• Your social media profile image
                    <br>• A Gravatar URL
                    <br>• Any public image URL
                </p>
            </div>
        </div>
    </div>

    <!-- Dashboard (Initially hidden) -->
    <div id="dashboard" class="h-screen flex flex-col hidden">
        <!-- Top Bar -->
        <div class="h-16 glass border-b border-white/20 flex items-center justify-between px-6">
            <!-- Search Bar -->
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                    <input
                        type="text"
                        placeholder="Search for songs, artists, or albums..."
                        class="w-full pl-10 pr-4 py-2 bg-white/20 border border-white/30 rounded-full text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                    />
                </div>
            </div>

            <!-- User Menu -->
            <div class="flex items-center gap-4">
                <button class="p-2 hover:bg-white/20 rounded-full">
                    <i data-lucide="bell" class="w-5 h-5 text-gray-600"></i>
                </button>
                
                <div class="flex items-center gap-2 p-2 rounded-full hover:bg-white/20">
                    <img id="userAvatar" src="" alt="User Avatar" class="w-8 h-8 rounded-full hidden">
                    <div id="defaultAvatar" class="w-8 h-8 red-gradient rounded-full flex items-center justify-center">
                        <i data-lucide="user" class="w-4 h-4 text-white"></i>
                    </div>
                    <div class="flex flex-col">
                        <span id="userName" class="text-sm font-medium text-gray-700">Demo User</span>
                        <span id="userProvider" class="text-xs text-green-600 hidden">✓ via Google</span>
                    </div>
                    <button onclick="showLogin()" class="text-xs text-red-600 hover:text-red-800 ml-2">Logout</button>
                </div>
            </div>
        </div>
        
        <!-- Main Layout -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Sidebar -->
            <div class="w-64 glass border-r border-white/20 flex flex-col">
                <!-- Logo -->
                <div class="p-6 border-b border-white/20">
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 red-gradient rounded-lg flex items-center justify-center">
                            <i data-lucide="music" class="w-5 h-5 text-white"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-800">MP3 World</span>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="flex-1 overflow-y-auto p-4 space-y-6">
                    <div class="space-y-2">
                        <button id="nav-home" onclick="showView('home')" class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left bg-red-600 text-white">
                            <i data-lucide="home" class="w-5 h-5"></i>
                            <span class="font-medium">Home</span>
                        </button>
                        <button id="nav-search" onclick="showView('search')" class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-700 hover:bg-white/30">
                            <i data-lucide="search" class="w-5 h-5"></i>
                            <span class="font-medium">Search</span>
                        </button>
                        <button id="nav-library" onclick="showView('library')" class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-700 hover:bg-white/30">
                            <i data-lucide="library" class="w-5 h-5"></i>
                            <span class="font-medium">Your Library</span>
                        </button>
                    </div>

                    <div>
                        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Library</h3>
                        <div class="space-y-1">
                            <button onclick="createNewPlaylist()" class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-600 hover:bg-white/30">
                                <i data-lucide="plus" class="w-4 h-4"></i>
                                <span class="text-sm">Create Playlist</span>
                            </button>
                            <button onclick="showView('liked')" class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-600 hover:bg-white/30">
                                <i data-lucide="heart" class="w-4 h-4"></i>
                                <span class="text-sm">Liked Songs</span>
                                <span id="likedCount" class="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full ml-auto">0</span>
                            </button>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Playlists</h3>
                        <div class="space-y-1">
                            <button class="w-full text-left px-3 py-2 rounded-lg text-gray-600 hover:bg-white/30">
                                <span class="text-sm">My Playlist #1</span>
                            </button>
                            <button class="w-full text-left px-3 py-2 rounded-lg text-gray-600 hover:bg-white/30">
                                <span class="text-sm">Chill Vibes</span>
                            </button>
                            <button class="w-full text-left px-3 py-2 rounded-lg text-gray-600 hover:bg-white/30">
                                <span class="text-sm">Workout Mix</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="flex-1 overflow-y-auto">
                <!-- Search Bar (Always visible) -->
                <div class="p-6 border-b border-white/20">
                    <div class="relative max-w-md">
                        <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                        <input
                            id="searchInput"
                            type="text"
                            placeholder="Search for songs, artists, or albums..."
                            class="w-full pl-10 pr-4 py-2 bg-white/20 border border-white/30 rounded-full text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
                            oninput="performSearch(this.value)"
                        />
                        <button onclick="clearSearch()" id="clearSearchBtn" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 hidden">
                            <i data-lucide="x" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>

                <div class="p-8">
                    <!-- Authentication Status -->
                    <div id="authStatus" class="glass rounded-lg p-4 mb-6 hidden">
                        <div class="flex items-center gap-2 mb-3">
                            <i data-lucide="check-circle" class="w-5 h-5 text-green-500"></i>
                            <span id="authStatusTitle" class="text-sm font-medium text-gray-900">Authentication Successful</span>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-xs">
                            <div>
                                <span class="font-medium text-gray-600">Provider:</span>
                                <span id="authProvider" class="ml-2 text-gray-900 capitalize">Custom</span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-600">Email Verified:</span>
                                <span class="ml-2 text-gray-900">✅ Yes</span>
                            </div>
                            <div class="col-span-2" id="userIdDisplay">
                                <span class="font-medium text-gray-600">User ID:</span>
                                <span id="displayUserId" class="ml-2 text-gray-900 font-mono">custom_123</span>
                            </div>
                            <div class="col-span-2" id="loginTimeDisplay" style="display: none;">
                                <span class="font-medium text-gray-600">Login Time:</span>
                                <span id="displayLoginTime" class="ml-2 text-gray-900 text-xs"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Dynamic Content Area -->
                    <div id="contentArea">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                </div>


            </div>
        </div>
        
        <!-- Bottom Player -->
        <div class="h-20 glass border-t border-white/20 flex items-center justify-between px-4">
            <!-- Current Track Info -->
            <div class="flex items-center gap-3 w-1/4">
                <div id="currentTrackImage" class="w-14 h-14 red-gradient rounded-lg flex items-center justify-center">
                    <i data-lucide="music" class="w-6 h-6 text-white"></i>
                </div>
                <div class="min-w-0">
                    <p id="currentTrackTitle" class="font-medium text-gray-900 truncate">Select a song to play</p>
                    <p id="currentTrackArtist" class="text-sm text-gray-600 truncate">No song selected</p>
                </div>
                <button class="p-2 text-red-600">
                    <i data-lucide="heart" class="w-4 h-4 fill-current"></i>
                </button>
            </div>

            <!-- Player Controls -->
            <div class="flex flex-col items-center w-1/2 max-w-md">
                <!-- Control Buttons -->
                <div class="flex items-center gap-2 mb-2">
                    <button class="p-2 text-gray-400 hover:text-red-600">
                        <i data-lucide="shuffle" class="w-4 h-4"></i>
                    </button>
                    
                    <button onclick="previousTrack()" class="p-2 text-gray-700 hover:text-red-600">
                        <i data-lucide="skip-back" class="w-5 h-5"></i>
                    </button>

                    <button id="playPauseBtn" onclick="togglePlayPause()" class="w-10 h-10 rounded-full red-gradient text-white flex items-center justify-center hover:scale-105">
                        <i data-lucide="play" class="w-5 h-5 ml-1"></i>
                    </button>

                    <button onclick="nextTrack()" class="p-2 text-gray-700 hover:text-red-600">
                        <i data-lucide="skip-forward" class="w-5 h-5"></i>
                    </button>
                    
                    <button class="p-2 text-gray-400 hover:text-red-600">
                        <i data-lucide="repeat" class="w-4 h-4"></i>
                    </button>
                </div>

                <!-- Progress Bar -->
                <div class="flex items-center gap-2 w-full">
                    <span id="currentTime" class="text-xs text-gray-500">0:00</span>
                    <div class="flex-1 h-1 bg-gray-300 rounded-full overflow-hidden cursor-pointer" onclick="seekAudio(event)">
                        <div id="progressBar" class="h-full bg-red-600 transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <span id="totalTime" class="text-xs text-gray-500">0:00</span>
                </div>
            </div>

            <!-- Volume and Additional Controls -->
            <div class="flex items-center gap-2 w-1/4 justify-end">
                <div class="flex items-center gap-2">
                    <i data-lucide="volume-2" class="w-4 h-4 text-gray-400"></i>
                    <div class="w-20 h-1 bg-gray-300 rounded-full overflow-hidden">
                        <div class="h-full bg-red-600 transition-all duration-300" style="width: 75%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Simulated Google user data
        const mockGoogleUser = {
            id: "google_" + Math.random().toString(36).substr(2, 9),
            googleId: "1234567890123456789",
            name: "John Doe",
            email: "<EMAIL>",
            picture: "https://lh3.googleusercontent.com/a/default-user=s96-c",
            verified_email: true,
            provider: "google"
        };

        // Local music library from uploaded files
        const musicLibrary = [
            {
                id: 1,
                title: "A Ninode en daivame",
                artist: "Unknown Artist",
                album: "Local Collection",
                duration: "3:45",
                file: "./A Ninode en daivame.wav2.wav",
                image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop"
            },
            {
                id: 2,
                title: "A Paro",
                artist: "Unknown Artist",
                album: "Local Collection",
                duration: "4:12",
                file: "./A Paro.wav",
                image: "https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=300&h=300&fit=crop"
            },
            {
                id: 3,
                title: "AnnnnRosh",
                artist: "Unknown Artist",
                album: "Local Collection",
                duration: "3:28",
                file: "./AnnnnRosh.mp3",
                image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=300&fit=crop"
            },
            {
                id: 4,
                title: "Aremb Final out",
                artist: "Unknown Artist",
                album: "Local Collection",
                duration: "5:15",
                file: "./Aremb Final out.mp3",
                image: "https://images.unsplash.com/photo-1415201364774-f6f0bb35f28f?w=300&h=300&fit=crop"
            },
            {
                id: 5,
                title: "Arosh",
                artist: "Unknown Artist",
                album: "Local Collection",
                duration: "3:52",
                file: "./Arosh.wav",
                image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop"
            },
            {
                id: 6,
                title: "AtoZ Remembrance Hillsong",
                artist: "Hillsong",
                album: "Worship Collection",
                duration: "4:33",
                file: "./AtoZRemembrance hillsong.wav",
                image: "https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=300&h=300&fit=crop"
            },
            {
                id: 7,
                title: "Chattan Cover 2",
                artist: "Unknown Artist",
                album: "Local Collection",
                duration: "3:18",
                file: "./Chattan Cover 2.wav",
                image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=300&fit=crop"
            },
            {
                id: 8,
                title: "A New Wine",
                artist: "Unknown Artist",
                album: "Local Collection",
                duration: "4:07",
                file: "./anew wine.wav",
                image: "https://images.unsplash.com/photo-1415201364774-f6f0bb35f28f?w=300&h=300&fit=crop"
            },
            {
                id: 9,
                title: "Avega Tere Lahu Ka",
                artist: "Unknown Artist",
                album: "Local Collection",
                duration: "3:41",
                file: "./avega Tere lahu ka .wav",
                image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop"
            }
        ];

        // Audio player state
        let currentAudio = null;
        let currentTrackIndex = 0;
        let isPlaying = false;
        let isShuffled = false;
        let repeatMode = 0; // 0: off, 1: all, 2: one

        // App state
        let currentView = 'home';
        let likedSongs = JSON.parse(localStorage.getItem('mp3_world_liked_songs') || '[]');
        let playlists = JSON.parse(localStorage.getItem('mp3_world_playlists') || '[]');
        let searchResults = [];
        let searchQuery = '';

        // Initialize music player
        function initializeMusicPlayer() {
            updateLikedCount();
            showView('home');
            if (musicLibrary.length > 0) {
                loadTrack(0);
                showNotification(`🎵 Loaded ${musicLibrary.length} songs from your music library!`);
            } else {
                showNotification('❌ No music files found in the library.');
            }
        }

        // Show different views
        function showView(view) {
            currentView = view;
            updateNavigation();

            const contentArea = document.getElementById('contentArea');

            switch(view) {
                case 'home':
                    contentArea.innerHTML = getHomeContent();
                    populateTracksList();
                    break;
                case 'search':
                    contentArea.innerHTML = getSearchContent();
                    if (searchQuery) {
                        displaySearchResults();
                    }
                    break;
                case 'library':
                    contentArea.innerHTML = getLibraryContent();
                    break;
                case 'liked':
                    contentArea.innerHTML = getLikedSongsContent();
                    displayLikedSongs();
                    break;
                default:
                    contentArea.innerHTML = getHomeContent();
                    populateTracksList();
            }

            lucide.createIcons();
        }

        // Update navigation active state
        function updateNavigation() {
            // Reset all nav buttons
            document.querySelectorAll('[id^="nav-"]').forEach(btn => {
                btn.className = 'w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-gray-700 hover:bg-white/30';
            });

            // Set active nav button
            const activeNav = document.getElementById(`nav-${currentView}`);
            if (activeNav) {
                activeNav.className = 'w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left bg-red-600 text-white';
            }
        }

        // Search functionality
        function performSearch(query) {
            searchQuery = query.toLowerCase().trim();

            // Show/hide clear button
            const clearBtn = document.getElementById('clearSearchBtn');
            if (searchQuery === '') {
                clearBtn.classList.add('hidden');
                searchResults = [];
                if (currentView === 'search') {
                    displaySearchResults();
                }
                return;
            } else {
                clearBtn.classList.remove('hidden');
            }

            // Search in music library
            searchResults = musicLibrary.filter(track =>
                track.title.toLowerCase().includes(searchQuery) ||
                track.artist.toLowerCase().includes(searchQuery) ||
                track.album.toLowerCase().includes(searchQuery)
            );

            // Auto-switch to search view if searching
            if (currentView !== 'search') {
                showView('search');
            } else {
                displaySearchResults();
            }
        }

        // Clear search
        function clearSearch() {
            document.getElementById('searchInput').value = '';
            document.getElementById('clearSearchBtn').classList.add('hidden');
            searchQuery = '';
            searchResults = [];
            if (currentView === 'search') {
                displaySearchResults();
            }
        }

        // Display search results
        function displaySearchResults() {
            const resultsContainer = document.getElementById('searchResults');
            if (!resultsContainer) return;

            if (searchQuery === '') {
                resultsContainer.innerHTML = `
                    <div class="text-center py-12">
                        <i data-lucide="search" class="w-16 h-16 text-gray-400 mx-auto mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Search for music</h3>
                        <p class="text-gray-600">Find your favorite songs, artists, and albums</p>
                    </div>
                `;
                lucide.createIcons();
                return;
            }

            if (searchResults.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="text-center py-12">
                        <i data-lucide="music-off" class="w-16 h-16 text-gray-400 mx-auto mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                        <p class="text-gray-600">Try searching for something else</p>
                    </div>
                `;
                lucide.createIcons();
                return;
            }

            resultsContainer.innerHTML = `
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    Found ${searchResults.length} result${searchResults.length !== 1 ? 's' : ''} for "${searchQuery}"
                </h3>
                <div class="glass rounded-xl overflow-hidden">
                    <div class="p-4 border-b border-white/20">
                        <div class="grid grid-cols-12 gap-4 text-sm font-medium text-gray-600 uppercase tracking-wider">
                            <div class="col-span-1">#</div>
                            <div class="col-span-5">Title</div>
                            <div class="col-span-3">Album</div>
                            <div class="col-span-2">Duration</div>
                            <div class="col-span-1"></div>
                        </div>
                    </div>
                    <div class="divide-y divide-white/10" id="searchTracksList">
                        <!-- Search results will be populated -->
                    </div>
                </div>
            `;

            populateSearchResults();
            lucide.createIcons();
        }

        // Populate search results
        function populateSearchResults() {
            const tracksList = document.getElementById('searchTracksList');
            if (!tracksList) return;

            tracksList.innerHTML = '';

            searchResults.forEach((track, index) => {
                const originalIndex = musicLibrary.findIndex(t => t.id === track.id);
                const isCurrentTrack = originalIndex === currentTrackIndex && isPlaying;
                const trackElement = document.createElement('div');
                trackElement.className = `p-4 hover:bg-white/20 transition-colors cursor-pointer group ${isCurrentTrack ? 'track-playing' : ''}`;
                trackElement.onclick = () => playTrack(originalIndex);

                const playIcon = isCurrentTrack ?
                    '<div class="audio-visualizer"><span></span><span></span><span></span></div>' :
                    `<span class="track-number text-gray-600 group-hover:hidden">${index + 1}</span>
                     <i data-lucide="play" class="w-4 h-4 text-red-600 hidden group-hover:block"></i>`;

                trackElement.innerHTML = `
                    <div class="grid grid-cols-12 gap-4 items-center">
                        <div class="col-span-1">${playIcon}</div>
                        <div class="col-span-5 flex items-center gap-3">
                            <div class="w-10 h-10 red-gradient rounded flex items-center justify-center">
                                <i data-lucide="music" class="w-5 h-5 text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">${track.title}</p>
                                <p class="text-sm text-gray-600">${track.artist}</p>
                            </div>
                        </div>
                        <div class="col-span-3">
                            <p class="text-gray-600">${track.album}</p>
                        </div>
                        <div class="col-span-2">
                            <p class="text-gray-600">${track.duration}</p>
                        </div>
                        <div class="col-span-1 flex items-center gap-2">
                            <button onclick="toggleLike(${track.id}); event.stopPropagation();" class="p-1 opacity-0 group-hover:opacity-100 hover:text-red-600">
                                <i data-lucide="heart" class="w-4 h-4 ${likedSongs.includes(track.id) ? 'fill-current text-red-600' : ''}"></i>
                            </button>
                        </div>
                    </div>
                `;

                tracksList.appendChild(trackElement);
            });

            lucide.createIcons();
        }

        // Show notification
        function showNotification(message) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 glass rounded-lg p-4 shadow-lg z-50 max-w-sm';
            notification.innerHTML = `
                <div class="flex items-center gap-3">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">${message}</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);
            lucide.createIcons();

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Populate tracks list
        function populateTracksList() {
            const tracksList = document.getElementById('musicTracksList');
            if (!tracksList) return;
            tracksList.innerHTML = '';

            musicLibrary.forEach((track, index) => {
                const trackElement = document.createElement('div');
                const isCurrentTrack = index === currentTrackIndex && isPlaying;
                trackElement.className = `p-4 hover:bg-white/20 transition-colors cursor-pointer group ${isCurrentTrack ? 'track-playing' : ''}`;
                trackElement.onclick = () => playTrack(index);
                trackElement.id = `track-${index}`;

                const playIcon = isCurrentTrack ?
                    '<div class="audio-visualizer"><span></span><span></span><span></span></div>' :
                    `<span class="track-number text-gray-600 group-hover:hidden">${index + 1}</span>
                     <i data-lucide="play" class="w-4 h-4 text-red-600 hidden group-hover:block"></i>`;

                trackElement.innerHTML = `
                    <div class="grid grid-cols-12 gap-4 items-center">
                        <div class="col-span-1">
                            ${playIcon}
                        </div>
                        <div class="col-span-5 flex items-center gap-3">
                            <div class="w-10 h-10 red-gradient rounded flex items-center justify-center">
                                <i data-lucide="music" class="w-5 h-5 text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">${track.title}</p>
                                <p class="text-sm text-gray-600">${track.artist}</p>
                            </div>
                        </div>
                        <div class="col-span-3">
                            <p class="text-gray-600">${track.album}</p>
                        </div>
                        <div class="col-span-2">
                            <p class="text-gray-600">${track.duration}</p>
                        </div>
                        <div class="col-span-1 flex items-center gap-2">
                            <button onclick="toggleLike(${track.id}); event.stopPropagation();" class="p-1 opacity-0 group-hover:opacity-100 hover:text-red-600">
                                <i data-lucide="heart" class="w-4 h-4 ${likedSongs.includes(track.id) ? 'fill-current text-red-600' : ''}"></i>
                            </button>
                        </div>
                    </div>
                `;

                tracksList.appendChild(trackElement);
            });

            // Re-initialize Lucide icons
            lucide.createIcons();
        }

        // Update track list visual state
        function updateTrackListState() {
            // Remove previous playing state
            document.querySelectorAll('.track-playing').forEach(el => {
                el.classList.remove('track-playing');
            });

            // Add playing state to current track
            if (isPlaying) {
                const currentTrackElement = document.getElementById(`track-${currentTrackIndex}`);
                if (currentTrackElement) {
                    currentTrackElement.classList.add('track-playing');
                    // Update the play indicator
                    const playIndicator = currentTrackElement.querySelector('.col-span-1');
                    playIndicator.innerHTML = '<div class="audio-visualizer"><span></span><span></span><span></span></div>';
                }
            }
        }

        // Toggle like status
        function toggleLike(trackId) {
            const index = likedSongs.indexOf(trackId);
            if (index > -1) {
                likedSongs.splice(index, 1);
                showNotification('❤️ Removed from liked songs');
            } else {
                likedSongs.push(trackId);
                showNotification('💖 Added to liked songs');
            }

            // Save to localStorage
            localStorage.setItem('mp3_world_liked_songs', JSON.stringify(likedSongs));
            updateLikedCount();

            // Refresh current view if needed
            if (currentView === 'liked') {
                displayLikedSongs();
            } else if (currentView === 'home') {
                // Update the liked songs count in featured playlists
                showView('home');
            }
        }

        // Update liked count
        function updateLikedCount() {
            const countElement = document.getElementById('likedCount');
            if (countElement) {
                countElement.textContent = likedSongs.length;
            }
        }

        // Display liked songs
        function displayLikedSongs() {
            const likedSongsList = document.getElementById('likedSongsList');
            if (!likedSongsList) return;

            if (likedSongs.length === 0) {
                likedSongsList.innerHTML = `
                    <div class="text-center py-12">
                        <i data-lucide="heart" class="w-16 h-16 text-gray-400 mx-auto mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No liked songs yet</h3>
                        <p class="text-gray-600">Songs you like will appear here</p>
                        <button onclick="showView('home')" class="mt-4 px-6 py-2 red-gradient text-white rounded-full font-medium hover:opacity-90 transition-opacity">
                            Browse Music
                        </button>
                    </div>
                `;
                lucide.createIcons();
                return;
            }

            const likedTracks = musicLibrary.filter(track => likedSongs.includes(track.id));

            likedSongsList.innerHTML = `
                <div class="glass rounded-xl overflow-hidden">
                    <div class="p-4 border-b border-white/20">
                        <div class="grid grid-cols-12 gap-4 text-sm font-medium text-gray-600 uppercase tracking-wider">
                            <div class="col-span-1">#</div>
                            <div class="col-span-5">Title</div>
                            <div class="col-span-3">Album</div>
                            <div class="col-span-2">Duration</div>
                            <div class="col-span-1"></div>
                        </div>
                    </div>
                    <div class="divide-y divide-white/10" id="likedTracksList">
                        <!-- Liked tracks will be populated -->
                    </div>
                </div>
            `;

            const likedTracksList = document.getElementById('likedTracksList');

            likedTracks.forEach((track, index) => {
                const originalIndex = musicLibrary.findIndex(t => t.id === track.id);
                const isCurrentTrack = originalIndex === currentTrackIndex && isPlaying;
                const trackElement = document.createElement('div');
                trackElement.className = `p-4 hover:bg-white/20 transition-colors cursor-pointer group ${isCurrentTrack ? 'track-playing' : ''}`;
                trackElement.onclick = () => playTrack(originalIndex);

                const playIcon = isCurrentTrack ?
                    '<div class="audio-visualizer"><span></span><span></span><span></span></div>' :
                    `<span class="track-number text-gray-600 group-hover:hidden">${index + 1}</span>
                     <i data-lucide="play" class="w-4 h-4 text-red-600 hidden group-hover:block"></i>`;

                trackElement.innerHTML = `
                    <div class="grid grid-cols-12 gap-4 items-center">
                        <div class="col-span-1">${playIcon}</div>
                        <div class="col-span-5 flex items-center gap-3">
                            <div class="w-10 h-10 red-gradient rounded flex items-center justify-center">
                                <i data-lucide="music" class="w-5 h-5 text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">${track.title}</p>
                                <p class="text-sm text-gray-600">${track.artist}</p>
                            </div>
                        </div>
                        <div class="col-span-3">
                            <p class="text-gray-600">${track.album}</p>
                        </div>
                        <div class="col-span-2">
                            <p class="text-gray-600">${track.duration}</p>
                        </div>
                        <div class="col-span-1 flex items-center gap-2">
                            <button onclick="toggleLike(${track.id}); event.stopPropagation();" class="p-1 opacity-0 group-hover:opacity-100 text-red-600">
                                <i data-lucide="heart" class="w-4 h-4 fill-current"></i>
                            </button>
                        </div>
                    </div>
                `;

                likedTracksList.appendChild(trackElement);
            });

            lucide.createIcons();
        }

        // Play all songs
        function playAllSongs() {
            if (musicLibrary.length > 0) {
                playTrack(0);
                showNotification('🎵 Playing all songs');
            }
        }

        // Play liked songs
        function playLikedSongs() {
            if (likedSongs.length > 0) {
                const firstLikedTrack = musicLibrary.find(track => track.id === likedSongs[0]);
                const firstLikedIndex = musicLibrary.findIndex(track => track.id === likedSongs[0]);
                if (firstLikedIndex !== -1) {
                    playTrack(firstLikedIndex);
                    showNotification('💖 Playing liked songs');
                }
            }
        }

        // Create new playlist
        function createNewPlaylist() {
            const playlistName = prompt('Enter playlist name:');
            if (playlistName && playlistName.trim()) {
                const newPlaylist = {
                    id: Date.now(),
                    name: playlistName.trim(),
                    songs: [],
                    created: new Date().toISOString()
                };

                playlists.push(newPlaylist);
                localStorage.setItem('mp3_world_playlists', JSON.stringify(playlists));
                showNotification(`📝 Created playlist "${playlistName}"`);
            }
        }

        // Load track
        function loadTrack(index) {
            if (index < 0 || index >= musicLibrary.length) return;

            currentTrackIndex = index;
            const track = musicLibrary[index];

            // Stop current audio if playing
            if (currentAudio) {
                currentAudio.pause();
                currentAudio = null;
            }

            // Create new audio element
            currentAudio = new Audio(track.file);

            // Update UI
            document.getElementById('currentTrackTitle').textContent = track.title;
            document.getElementById('currentTrackArtist').textContent = track.artist;
            document.getElementById('totalTime').textContent = track.duration;

            // Add event listeners
            currentAudio.addEventListener('loadedmetadata', updateDuration);
            currentAudio.addEventListener('timeupdate', updateProgress);
            currentAudio.addEventListener('ended', handleTrackEnd);
            currentAudio.addEventListener('error', handleAudioError);

            // Load the audio
            currentAudio.load();
        }

        // Play specific track
        function playTrack(index) {
            loadTrack(index);
            setTimeout(() => {
                togglePlayPause();
            }, 100);
        }

        // Toggle play/pause
        function togglePlayPause() {
            if (!currentAudio) {
                if (musicLibrary.length > 0) {
                    loadTrack(0);
                    setTimeout(togglePlayPause, 100);
                }
                return;
            }

            if (isPlaying) {
                currentAudio.pause();
                isPlaying = false;
                document.querySelector('#playPauseBtn i').setAttribute('data-lucide', 'play');
                document.querySelector('#playPauseBtn i').classList.add('ml-1');
            } else {
                currentAudio.play().then(() => {
                    isPlaying = true;
                    document.querySelector('#playPauseBtn i').setAttribute('data-lucide', 'pause');
                    document.querySelector('#playPauseBtn i').classList.remove('ml-1');
                }).catch(error => {
                    console.error('Error playing audio:', error);
                    alert('Error playing audio file. Please check if the file exists and is accessible.');
                });
            }

            // Update track list visual state
            updateTrackListState();
            lucide.createIcons();
        }

        // Previous track
        function previousTrack() {
            let newIndex = currentTrackIndex - 1;
            if (newIndex < 0) {
                newIndex = musicLibrary.length - 1;
            }
            playTrack(newIndex);
        }

        // Next track
        function nextTrack() {
            let newIndex = currentTrackIndex + 1;
            if (newIndex >= musicLibrary.length) {
                newIndex = 0;
            }
            playTrack(newIndex);
        }

        // Update progress
        function updateProgress() {
            if (!currentAudio) return;

            const progress = (currentAudio.currentTime / currentAudio.duration) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
            document.getElementById('currentTime').textContent = formatTime(currentAudio.currentTime);
        }

        // Update duration
        function updateDuration() {
            if (!currentAudio) return;
            document.getElementById('totalTime').textContent = formatTime(currentAudio.duration);
        }

        // Seek audio
        function seekAudio(event) {
            if (!currentAudio) return;

            const progressBar = event.currentTarget;
            const rect = progressBar.getBoundingClientRect();
            const percent = (event.clientX - rect.left) / rect.width;
            currentAudio.currentTime = percent * currentAudio.duration;
        }

        // Handle track end
        function handleTrackEnd() {
            if (repeatMode === 2) {
                // Repeat one
                currentAudio.currentTime = 0;
                currentAudio.play();
            } else if (repeatMode === 1 || currentTrackIndex < musicLibrary.length - 1) {
                // Repeat all or not last track
                nextTrack();
            } else {
                // Stop playing
                isPlaying = false;
                document.querySelector('#playPauseBtn i').setAttribute('data-lucide', 'play');
                document.querySelector('#playPauseBtn i').classList.add('ml-1');
                lucide.createIcons();
            }
        }

        // Handle audio error
        function handleAudioError(error) {
            console.error('Audio error:', error);
            alert(`Error loading audio file: ${musicLibrary[currentTrackIndex].title}\nPlease check if the file exists and is accessible.`);
        }

        // Format time
        function formatTime(seconds) {
            if (isNaN(seconds)) return '0:00';
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        // Content templates
        function getHomeContent() {
            return `
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">
                        Good afternoon<span id="welcomeName"></span>
                    </h1>
                    <p class="text-gray-600">Ready to discover your next favorite song?</p>
                    <div id="googleConnected" class="flex items-center gap-2 mt-2 hidden">
                        <span class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                            ✓ Connected via Google
                        </span>
                    </div>
                </div>

                <!-- Featured Playlists -->
                <section class="mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Featured Playlists</h2>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="glass rounded-xl p-4 hover:bg-white/40 transition-all duration-200 cursor-pointer group">
                            <div class="relative mb-4">
                                <div class="w-full aspect-square bg-gradient-to-br from-red-400 to-red-600 rounded-lg flex items-center justify-center">
                                    <i data-lucide="music" class="w-12 h-12 text-white"></i>
                                </div>
                                <button onclick="playAllSongs()" class="absolute bottom-2 right-2 w-12 h-12 red-gradient rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:scale-105">
                                    <i data-lucide="play" class="w-5 h-5 ml-1"></i>
                                </button>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-1">All Songs</h3>
                            <p class="text-sm text-gray-600">${musicLibrary.length} songs in your library</p>
                        </div>

                        <div class="glass rounded-xl p-4 hover:bg-white/40 transition-all duration-200 cursor-pointer group" onclick="showView('liked')">
                            <div class="relative mb-4">
                                <div class="w-full aspect-square bg-gradient-to-br from-pink-400 to-pink-600 rounded-lg flex items-center justify-center">
                                    <i data-lucide="heart" class="w-12 h-12 text-white"></i>
                                </div>
                                <button class="absolute bottom-2 right-2 w-12 h-12 red-gradient rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:scale-105">
                                    <i data-lucide="play" class="w-5 h-5 ml-1"></i>
                                </button>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-1">Liked Songs</h3>
                            <p class="text-sm text-gray-600">${likedSongs.length} liked songs</p>
                        </div>

                        <div class="glass rounded-xl p-4 hover:bg-white/40 transition-all duration-200 cursor-pointer group">
                            <div class="relative mb-4">
                                <div class="w-full aspect-square bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center">
                                    <i data-lucide="headphones" class="w-12 h-12 text-white"></i>
                                </div>
                                <button class="absolute bottom-2 right-2 w-12 h-12 red-gradient rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:scale-105">
                                    <i data-lucide="play" class="w-5 h-5 ml-1"></i>
                                </button>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-1">Recently Played</h3>
                            <p class="text-sm text-gray-600">Your recent listening history</p>
                        </div>

                        <div class="glass rounded-xl p-4 hover:bg-white/40 transition-all duration-200 cursor-pointer group" onclick="createNewPlaylist()">
                            <div class="relative mb-4">
                                <div class="w-full aspect-square bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center">
                                    <i data-lucide="plus" class="w-12 h-12 text-white"></i>
                                </div>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-1">Create Playlist</h3>
                            <p class="text-sm text-gray-600">Make your own playlist</p>
                        </div>
                    </div>
                </section>

                <!-- All Songs -->
                <section>
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">All Songs</h2>
                    <div class="glass rounded-xl overflow-hidden">
                        <div class="p-4 border-b border-white/20">
                            <div class="grid grid-cols-12 gap-4 text-sm font-medium text-gray-600 uppercase tracking-wider">
                                <div class="col-span-1">#</div>
                                <div class="col-span-5">Title</div>
                                <div class="col-span-3">Album</div>
                                <div class="col-span-2">Duration</div>
                                <div class="col-span-1"></div>
                            </div>
                        </div>
                        <div class="divide-y divide-white/10" id="musicTracksList">
                            <!-- Music tracks will be populated by JavaScript -->
                        </div>
                    </div>
                </section>
            `;
        }

        function getSearchContent() {
            return `
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Search</h1>
                    <p class="text-gray-600">Find your favorite songs, artists, and albums</p>
                </div>

                <div id="searchResults">
                    <div class="text-center py-12">
                        <i data-lucide="search" class="w-16 h-16 text-gray-400 mx-auto mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Search for music</h3>
                        <p class="text-gray-600">Find your favorite songs, artists, and albums</p>
                    </div>
                </div>
            `;
        }

        function getLibraryContent() {
            return `
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Your Library</h1>
                    <p class="text-gray-600">Manage your music collection</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="glass rounded-xl p-6 hover:bg-white/40 transition-all duration-200 cursor-pointer" onclick="showView('liked')">
                        <div class="flex items-center gap-4 mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-pink-400 to-pink-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="heart" class="w-6 h-6 text-white"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Liked Songs</h3>
                                <p class="text-sm text-gray-600">${likedSongs.length} songs</p>
                            </div>
                        </div>
                    </div>

                    <div class="glass rounded-xl p-6 hover:bg-white/40 transition-all duration-200 cursor-pointer" onclick="showView('home')">
                        <div class="flex items-center gap-4 mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-red-400 to-red-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="music" class="w-6 h-6 text-white"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900">All Songs</h3>
                                <p class="text-sm text-gray-600">${musicLibrary.length} songs</p>
                            </div>
                        </div>
                    </div>

                    <div class="glass rounded-xl p-6 hover:bg-white/40 transition-all duration-200 cursor-pointer" onclick="createNewPlaylist()">
                        <div class="flex items-center gap-4 mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="plus" class="w-6 h-6 text-white"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Create Playlist</h3>
                                <p class="text-sm text-gray-600">Make a new playlist</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getLikedSongsContent() {
            return `
                <div class="mb-8">
                    <div class="flex items-center gap-4 mb-4">
                        <div class="w-16 h-16 bg-gradient-to-br from-pink-400 to-pink-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="heart" class="w-8 h-8 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Liked Songs</h1>
                            <p class="text-gray-600">${likedSongs.length} liked songs</p>
                        </div>
                    </div>
                    ${likedSongs.length > 0 ? `
                        <button onclick="playLikedSongs()" class="flex items-center gap-2 px-6 py-3 red-gradient text-white rounded-full font-medium hover:opacity-90 transition-opacity">
                            <i data-lucide="play" class="w-5 h-5"></i>
                            Play All
                        </button>
                    ` : ''}
                </div>

                <div id="likedSongsList">
                    <!-- Liked songs will be populated -->
                </div>
            `;
        }

        function authenticateWithGoogle() {
            // Step 1: Show Google OAuth consent screen
            showGoogleConsentScreen();
        }

        function showGoogleConsentScreen() {
            const authWindow = window.open('', '_blank', 'width=500,height=700,scrollbars=yes,resizable=yes');
            authWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Sign in - Google Accounts</title>
                    <style>
                        body {
                            font-family: 'Google Sans', Roboto, Arial, sans-serif;
                            margin: 0;
                            padding: 20px;
                            background: #fff;
                            color: #202124;
                        }
                        .container { max-width: 400px; margin: 0 auto; }
                        .google-logo {
                            width: 24px;
                            height: 24px;
                            margin-right: 8px;
                            vertical-align: middle;
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 30px;
                            padding: 20px 0;
                            border-bottom: 1px solid #dadce0;
                        }
                        .title {
                            font-size: 24px;
                            font-weight: 400;
                            margin: 16px 0;
                            color: #202124;
                        }
                        .subtitle {
                            font-size: 16px;
                            color: #5f6368;
                            margin-bottom: 24px;
                        }
                        .consent-text {
                            font-size: 14px;
                            color: #5f6368;
                            line-height: 1.4;
                            margin: 20px 0;
                        }
                        .button {
                            background: #1a73e8;
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 4px;
                            font-size: 14px;
                            font-weight: 500;
                            cursor: pointer;
                            width: 100%;
                            margin: 8px 0;
                        }
                        .button:hover { background: #1557b0; }
                        .cancel-btn {
                            background: transparent;
                            color: #1a73e8;
                            border: 1px solid #dadce0;
                        }
                        .cancel-btn:hover { background: #f8f9fa; }
                        .terms { font-size: 12px; color: #5f6368; margin-top: 20px; }
                        .terms a { color: #1a73e8; text-decoration: none; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <svg class="google-logo" viewBox="0 0 24 24">
                                <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                            </svg>
                            <span style="font-size: 16px; color: #5f6368;">Sign in with Google</span>
                        </div>

                        <div class="title">Choose an account</div>
                        <div class="subtitle">to continue to MP3 World</div>

                        <div onclick="selectGoogleAccount()" style="border: 1px solid #dadce0; border-radius: 8px; padding: 16px; margin: 16px 0; cursor: pointer; display: flex; align-items: center; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f8f9fa'" onmouseout="this.style.backgroundColor='white'">
                            <div style="width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #E53E3E 0%, #FC8181 100%); display: flex; align-items: center; justify-content: center; margin-right: 16px; color: white; font-weight: bold;">
                                L
                            </div>
                            <div>
                                <div style="font-weight: 500; color: #202124;">Libin Lalu</div>
                                <div style="font-size: 14px; color: #5f6368;"><EMAIL></div>
                            </div>
                        </div>

                        <div onclick="useAnotherAccount()" style="border: 1px solid #dadce0; border-radius: 8px; padding: 16px; margin: 16px 0; cursor: pointer; display: flex; align-items: center; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f8f9fa'" onmouseout="this.style.backgroundColor='white'">
                            <div style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #dadce0; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="#5f6368">
                                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z"/>
                                </svg>
                            </div>
                            <div>
                                <div style="font-weight: 500; color: #202124;">Use another account</div>
                            </div>
                        </div>

                        <div class="terms">
                            Before using this app, you can review MP3 World's
                            <a href="#">privacy policy</a> and <a href="#">terms of service</a>.
                        </div>
                    </div>

                    <script>
                        function selectGoogleAccount() {
                            // Show consent screen
                            showConsentScreen();
                        }

                        function useAnotherAccount() {
                            alert('Demo: This would show additional Google accounts or login form');
                        }

                        function showConsentScreen() {
                            document.body.innerHTML = \`
                                <div class="container">
                                    <div class="header">
                                        <svg class="google-logo" viewBox="0 0 24 24">
                                            <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                            <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                            <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                            <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                        </svg>
                                        <span style="font-size: 16px; color: #5f6368;">Sign in with Google</span>
                                    </div>

                                    <div class="title">Sign in to MP3 World</div>

                                    <div style="border: 1px solid #dadce0; border-radius: 8px; padding: 12px; margin: 16px 0; display: flex; align-items: center; background: #f8f9fa;">
                                        <div style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, #E53E3E 0%, #FC8181 100%); display: flex; align-items: center; justify-content: center; margin-right: 12px; color: white; font-weight: bold; font-size: 14px;">
                                            L
                                        </div>
                                        <div style="font-size: 14px; color: #5f6368;"><EMAIL></div>
                                        <div style="margin-left: auto;">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="#5f6368">
                                                <path d="M7 10l5 5 5-5z"/>
                                            </svg>
                                        </div>
                                    </div>

                                    <div class="consent-text">
                                        By continuing, Google will share your name, email address, and profile picture with MP3 World. See MP3 World's <a href="#" style="color: #1a73e8;">Privacy Policy</a> and <a href="#" style="color: #1a73e8;">Terms of Service</a>.
                                    </div>

                                    <div class="consent-text">
                                        You can manage Sign in with Google in your <a href="#" style="color: #1a73e8;">Google Account</a>.
                                    </div>

                                    <div style="display: flex; gap: 12px; margin-top: 32px;">
                                        <button class="button cancel-btn" onclick="window.close()">Cancel</button>
                                        <button class="button" onclick="proceedWithAuth()">Continue</button>
                                    </div>
                                </div>
                            \`;
                        }

                        function proceedWithAuth() {
                            // Show loading screen
                            document.body.innerHTML = \`
                                <div class="container" style="text-align: center; padding-top: 100px;">
                                    <svg width="48" height="48" viewBox="0 0 24 24" style="animation: spin 1s linear infinite;">
                                        <circle cx="12" cy="12" r="10" stroke="#1a73e8" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416" style="animation: dash 2s ease-in-out infinite alternate;"/>
                                    </svg>
                                    <div style="margin-top: 24px; font-size: 16px; color: #5f6368;">One moment please...</div>
                                </div>
                                <style>
                                    @keyframes spin {
                                        0% { transform: rotate(0deg); }
                                        100% { transform: rotate(360deg); }
                                    }
                                    @keyframes dash {
                                        0% { stroke-dashoffset: 31.416; }
                                        50% { stroke-dashoffset: 0; }
                                        100% { stroke-dashoffset: -31.416; }
                                    }
                                </style>
                            \`;

                            // Close window and proceed after 1 second
                            setTimeout(() => {
                                window.close();
                                window.opener.completeGoogleAuth();
                            }, 1000);
                        }
                    </script>
                </body>
                </html>
            `);
        }

        function authenticateWithApple() {
            // Simulate Apple Sign In
            const appleUser = {
                name: 'Apple User',
                email: '<EMAIL>',
                appleId: 'apple_' + Date.now(),
                profilePicture: null,
                authMethod: 'apple',
                verified_email: true,
                loginTime: new Date().toISOString()
            };

            // Store user data
            localStorage.setItem('mp3_world_user', JSON.stringify(appleUser));
            updateUserInterface(appleUser);
            showDashboard();

            setTimeout(() => {
                showNotification('🍎 Successfully signed in with Apple!');
            }, 500);
        }

        function authenticateWithEmail() {
            const email = document.getElementById('emailInput').value.trim();
            if (!email) {
                alert('Please enter your email address');
                return;
            }

            if (!email.includes('@')) {
                alert('Please enter a valid email address');
                return;
            }

            const emailUser = {
                name: email.split('@')[0],
                email: email,
                emailId: 'email_' + Date.now(),
                profilePicture: null,
                authMethod: 'email',
                verified_email: false,
                loginTime: new Date().toISOString()
            };

            // Store user data
            localStorage.setItem('mp3_world_user', JSON.stringify(emailUser));
            updateUserInterface(emailUser);
            showDashboard();

            setTimeout(() => {
                showNotification('📧 Successfully signed in with email!');
            }, 500);
        }

        function authenticateWithCustomId(event) {
            event.preventDefault();

            // Get form values
            const customId = document.getElementById('customUserId').value.trim();
            const customName = document.getElementById('customUserName').value.trim();
            const customEmail = document.getElementById('customUserEmail').value.trim();
            const customImage = document.getElementById('customUserImage').value.trim();

            // Validate inputs
            if (!customId || !customName || !customEmail) {
                alert('❌ Please fill in all required fields (ID, Name, Email)');
                return;
            }

            // Create custom user object
            const customUser = {
                id: "custom_" + customId,
                customId: customId,
                name: customName,
                email: customEmail,
                picture: customImage || `https://ui-avatars.com/api/?name=${encodeURIComponent(customName)}&background=e53e3e&color=fff&size=200`,
                verified_email: true,
                provider: "custom",
                loginTime: new Date().toISOString()
            };

            // Store user data
            localStorage.setItem('mp3_world_user', JSON.stringify(customUser));

            // Update UI
            updateUserInterface(customUser);

            // Clear form
            document.getElementById('customUserId').value = '';
            document.getElementById('customUserName').value = '';
            document.getElementById('customUserEmail').value = '';
            document.getElementById('customUserImage').value = '';

            showDashboard();

            // Show success message
            setTimeout(() => {
                alert(`✅ Successfully authenticated with Custom ID!\n\nUser: ${customUser.name}\nEmail: ${customUser.email}\nCustom ID: ${customUser.customId}\nLogin Time: ${new Date(customUser.loginTime).toLocaleString()}`);
            }, 500);
        }

        function completeGoogleAuth() {
            // Create realistic user data based on the Google account
            const googleUser = {
                name: 'Libin Lalu',
                email: '<EMAIL>',
                googleId: 'google_' + Date.now(),
                profilePicture: null,
                authMethod: 'google',
                verified_email: true,
                loginTime: new Date().toISOString()
            };

            // Store user data
            localStorage.setItem('mp3_world_user', JSON.stringify(googleUser));

            // Update UI with Google user info
            updateUserInterface(googleUser);

            showDashboard();

            // Show success notification
            setTimeout(() => {
                showNotification('🎉 Successfully signed in with Google!');
            }, 500);
        }

        function updateUserInterface(user) {
            // Update top bar user info
            document.getElementById('userName').textContent = user.name;
            document.getElementById('userProvider').textContent = '✓ via ' + user.provider;
            document.getElementById('userProvider').classList.remove('hidden');

            // Show user avatar if available
            if (user.picture) {
                document.getElementById('userAvatar').src = user.picture;
                document.getElementById('userAvatar').classList.remove('hidden');
                document.getElementById('defaultAvatar').classList.add('hidden');
            }

            // Update welcome message
            const firstName = user.name.split(' ')[0];
            document.getElementById('welcomeName').textContent = ', ' + firstName;

            // Show authentication status
            document.getElementById('authStatus').classList.remove('hidden');
            document.getElementById('authProvider').textContent = user.provider;

            // Update authentication status based on provider
            if (user.provider === 'google') {
                document.getElementById('authStatusTitle').textContent = 'Google Authentication Successful';
                document.getElementById('userIdDisplay').innerHTML = `
                    <span class="font-medium text-gray-600">Google ID:</span>
                    <span class="ml-2 text-gray-900 font-mono">${user.googleId}</span>
                `;
                document.getElementById('googleConnected').classList.remove('hidden');
            } else if (user.provider === 'custom') {
                document.getElementById('authStatusTitle').textContent = 'Custom Authentication Successful';
                document.getElementById('userIdDisplay').innerHTML = `
                    <span class="font-medium text-gray-600">Custom ID:</span>
                    <span class="ml-2 text-gray-900 font-mono">${user.customId}</span>
                `;

                // Show login time for custom authentication
                if (user.loginTime) {
                    document.getElementById('loginTimeDisplay').style.display = 'block';
                    document.getElementById('displayLoginTime').textContent = new Date(user.loginTime).toLocaleString();
                }

                // Show custom connected status
                const connectedElement = document.getElementById('googleConnected');
                connectedElement.innerHTML = `
                    <span class="px-3 py-1 bg-red-100 text-red-700 rounded-full text-sm font-medium">
                        ✓ Connected via Custom ID
                    </span>
                `;
                connectedElement.classList.remove('hidden');
            }
        }

        function showDashboard() {
            document.getElementById('loginPage').classList.add('hidden');
            document.getElementById('dashboard').classList.remove('hidden');
            // Initialize music player
            initializeMusicPlayer();
            // Re-initialize icons for the new content
            lucide.createIcons();
        }

        function showLogin() {
            // Clear stored user data
            localStorage.removeItem('mp3_world_user');

            // Reset UI
            document.getElementById('userName').textContent = 'Demo User';
            document.getElementById('userProvider').classList.add('hidden');
            document.getElementById('userAvatar').classList.add('hidden');
            document.getElementById('defaultAvatar').classList.remove('hidden');
            document.getElementById('welcomeName').textContent = '';
            document.getElementById('authStatus').classList.add('hidden');
            document.getElementById('googleConnected').classList.add('hidden');
            document.getElementById('loginTimeDisplay').style.display = 'none';

            // Clear custom authentication form
            document.getElementById('customUserId').value = '';
            document.getElementById('customUserName').value = '';
            document.getElementById('customUserEmail').value = '';
            document.getElementById('customUserImage').value = '';

            document.getElementById('dashboard').classList.add('hidden');
            document.getElementById('loginPage').classList.remove('hidden');
            // Re-initialize icons for the new content
            lucide.createIcons();
        }

        // Check if user is already logged in
        window.onload = function() {
            const storedUser = localStorage.getItem('mp3_world_user');
            if (storedUser) {
                const user = JSON.parse(storedUser);
                updateUserInterface(user);
                showDashboard();
            } else {
                // Initialize icons even if not logged in
                lucide.createIcons();
            }
        };
    </script>
</body>
</html>
