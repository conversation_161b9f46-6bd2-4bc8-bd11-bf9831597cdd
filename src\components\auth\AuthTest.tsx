'use client'

import { useSession } from 'next-auth/react'
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react'

export function AuthTest() {
  const { data: session, status } = useSession()

  if (status === 'loading') {
    return (
      <div className="glass rounded-lg p-4 mb-4">
        <div className="flex items-center gap-2">
          <AlertCircle className="w-5 h-5 text-yellow-500" />
          <span className="text-sm text-gray-600">Checking authentication status...</span>
        </div>
      </div>
    )
  }

  if (!session) {
    return (
      <div className="glass rounded-lg p-4 mb-4">
        <div className="flex items-center gap-2">
          <XCircle className="w-5 h-5 text-red-500" />
          <span className="text-sm text-gray-600">Not authenticated</span>
        </div>
      </div>
    )
  }

  return (
    <div className="glass rounded-lg p-4 mb-4">
      <div className="flex items-center gap-2 mb-3">
        <CheckCircle className="w-5 h-5 text-green-500" />
        <span className="text-sm font-medium text-gray-900">Authentication Successful</span>
      </div>
      
      <div className="space-y-2 text-xs">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="font-medium text-gray-600">Provider:</span>
            <span className="ml-2 text-gray-900 capitalize">{session.user.provider || 'Unknown'}</span>
          </div>
          <div>
            <span className="font-medium text-gray-600">Email Verified:</span>
            <span className="ml-2 text-gray-900">
              {session.user.verified_email ? '✅ Yes' : '❌ No'}
            </span>
          </div>
        </div>
        
        {session.user.googleId && (
          <div>
            <span className="font-medium text-gray-600">Google ID:</span>
            <span className="ml-2 text-gray-900 font-mono">{session.user.googleId}</span>
          </div>
        )}
        
        <div>
          <span className="font-medium text-gray-600">User ID:</span>
          <span className="ml-2 text-gray-900 font-mono">{session.user.id}</span>
        </div>
      </div>
    </div>
  )
}
