<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MP3 World - Simple Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #FFF5F5 0%, #FED7D7 50%, #FEB2B2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #E53E3E 0%, #FC8181 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        p {
            color: #666;
            margin-bottom: 30px;
        }
        .btn {
            background: linear-gradient(135deg, #E53E3E 0%, #FC8181 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: scale(1.05);
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🎵</div>
        <h1>MP3 World</h1>
        <p>Simple Test Version</p>
        
        <button class="btn" onclick="testGoogle()">Test Google Login</button>
        <button class="btn" onclick="testApple()">Test Apple Login</button>
        <button class="btn" onclick="testEmail()">Test Email Login</button>
        
        <div id="status" class="status"></div>
        
        <div id="dashboard" style="display: none; margin-top: 30px;">
            <h2>🎉 Welcome to MP3 World!</h2>
            <p>User: <span id="userName">Test User</span></p>
            <p>Authentication: <span id="authMethod">Working</span></p>
            <button class="btn" onclick="logout()">Logout</button>
        </div>
    </div>

    <script>
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + type;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        function testGoogle() {
            console.log('Google test clicked');
            showStatus('🔄 Testing Google authentication...', 'success');
            
            setTimeout(() => {
                document.getElementById('userName').textContent = 'Libin Lalu';
                document.getElementById('authMethod').textContent = 'Google';
                document.getElementById('dashboard').style.display = 'block';
                showStatus('✅ Google authentication successful!', 'success');
            }, 1000);
        }

        function testApple() {
            console.log('Apple test clicked');
            showStatus('🔄 Testing Apple authentication...', 'success');
            
            setTimeout(() => {
                document.getElementById('userName').textContent = 'Apple User';
                document.getElementById('authMethod').textContent = 'Apple';
                document.getElementById('dashboard').style.display = 'block';
                showStatus('✅ Apple authentication successful!', 'success');
            }, 1000);
        }

        function testEmail() {
            const email = prompt('Enter your email:');
            if (email) {
                console.log('Email test clicked:', email);
                showStatus('🔄 Testing email authentication...', 'success');
                
                setTimeout(() => {
                    document.getElementById('userName').textContent = email;
                    document.getElementById('authMethod').textContent = 'Email';
                    document.getElementById('dashboard').style.display = 'block';
                    showStatus('✅ Email authentication successful!', 'success');
                }, 1000);
            }
        }

        function logout() {
            document.getElementById('dashboard').style.display = 'none';
            showStatus('👋 Logged out successfully', 'success');
        }

        // Test if JavaScript is working
        console.log('✅ MP3 World Simple Test - JavaScript is working!');
        
        // Show ready message
        window.onload = function() {
            showStatus('🚀 MP3 World Test Ready - Click any button!', 'success');
        };
    </script>
</body>
</html>
