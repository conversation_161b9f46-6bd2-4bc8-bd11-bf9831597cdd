'use client'

import { useState } from 'react'
import { signIn } from 'next-auth/react'
import { ArrowLeft, <PERSON>r<PERSON>he<PERSON>, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/Button'

interface CustomAuthFormProps {
  onBack: () => void
}

export function CustomAuthForm({ onBack }: CustomAuthFormProps) {
  const [formData, setFormData] = useState({
    customId: '',
    name: '',
    email: '',
    image: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    try {
      // Validate required fields
      if (!formData.customId || !formData.name || !formData.email) {
        throw new Error('Please fill in all required fields')
      }

      // Create custom user data
      const customUser = {
        id: `custom_${formData.customId}`,
        customId: formData.customId,
        name: formData.name,
        email: formData.email,
        image: formData.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(formData.name)}&background=e53e3e&color=fff&size=200`,
        provider: 'custom',
        loginTime: new Date().toISOString()
      }

      // Store in localStorage for demo purposes
      localStorage.setItem('mp3_world_custom_user', JSON.stringify(customUser))

      // In a real app, you would send this to your backend
      // For now, we'll simulate a successful login
      alert(`✅ Custom authentication successful!\n\nUser: ${customUser.name}\nID: ${customUser.customId}\nEmail: ${customUser.email}`)
      
      // Redirect to dashboard (in a real app, this would be handled by NextAuth)
      window.location.reload()

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Authentication failed')
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (error) setError('')
  }

  return (
    <div>
      <div className="flex items-center gap-3 mb-6">
        <Button
          variant="ghost"
          onClick={onBack}
          className="p-2 hover:bg-white/20 rounded-full"
        >
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Custom Authentication</h3>
          <p className="text-sm text-gray-600">Enter your custom credentials</p>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
          <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
          <span className="text-sm text-red-700">{error}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="customId" className="block text-sm font-medium text-gray-700 mb-2">
            Custom ID *
          </label>
          <input
            type="text"
            id="customId"
            value={formData.customId}
            onChange={(e) => handleInputChange('customId', e.target.value)}
            placeholder="Enter your unique ID"
            className="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-primary focus:border-transparent"
            required
            disabled={isLoading}
          />
        </div>

        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Display Name *
          </label>
          <input
            type="text"
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="Enter your display name"
            className="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-primary focus:border-transparent"
            required
            disabled={isLoading}
          />
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            Email Address *
          </label>
          <input
            type="email"
            id="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            placeholder="Enter your email address"
            className="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-primary focus:border-transparent"
            required
            disabled={isLoading}
          />
        </div>

        <div>
          <label htmlFor="image" className="block text-sm font-medium text-gray-700 mb-2">
            Profile Image URL
          </label>
          <input
            type="url"
            id="image"
            value={formData.image}
            onChange={(e) => handleInputChange('image', e.target.value)}
            placeholder="https://example.com/your-image.jpg"
            className="w-full px-4 py-3 bg-white/30 border border-white/40 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-primary focus:border-transparent"
            disabled={isLoading}
          />
          <p className="text-xs text-gray-500 mt-1">Leave empty for auto-generated avatar</p>
        </div>

        <Button
          type="submit"
          disabled={isLoading}
          className="w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-medium bg-red-primary hover:bg-red-dark text-white shadow-md disabled:opacity-50"
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Authenticating...
            </>
          ) : (
            <>
              <UserCheck className="w-5 h-5" />
              Authenticate
            </>
          )}
        </Button>
      </form>

      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="text-sm font-medium text-blue-900 mb-2">💡 Custom Authentication Tips</h4>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• Use any unique identifier (username, employee ID, etc.)</li>
          <li>• Profile images can be any publicly accessible URL</li>
          <li>• Auto-generated avatars will be created if no image is provided</li>
          <li>• Your credentials are stored locally for this demo</li>
        </ul>
      </div>
    </div>
  )
}
