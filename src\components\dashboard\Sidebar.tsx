'use client'

import { useState } from 'react'
import { 
  Home, 
  Search, 
  Library, 
  Plus, 
  Heart, 
  Music, 
  Radio,
  Mic2,
  TrendingUp,
  Clock
} from 'lucide-react'
import { Button } from '@/components/ui/Button'

export function Sidebar() {
  const [activeItem, setActiveItem] = useState('home')

  const menuItems = [
    { id: 'home', label: 'Home', icon: Home },
    { id: 'search', label: 'Search', icon: Search },
    { id: 'library', label: 'Your Library', icon: Library },
  ]

  const libraryItems = [
    { id: 'create-playlist', label: 'Create Playlist', icon: Plus },
    { id: 'liked-songs', label: 'Liked Songs', icon: Heart },
    { id: 'recently-played', label: 'Recently Played', icon: Clock },
  ]

  const discoverItems = [
    { id: 'trending', label: 'Trending', icon: TrendingUp },
    { id: 'radio', label: 'Radio', icon: Radio },
    { id: 'podcasts', label: 'Podcasts', icon: Mic2 },
  ]

  const playlists = [
    'My Playlist #1',
    'Chill Vibes',
    'Workout Mix',
    'Road Trip Songs',
    'Study Music',
  ]

  return (
    <div className="w-64 glass border-r border-white/20 flex flex-col">
      {/* Logo */}
      <div className="p-6 border-b border-white/20">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-red-gradient rounded-lg flex items-center justify-center">
            <Music className="w-5 h-5 text-white" />
          </div>
          <span className="text-xl font-bold text-gray-800">MusicStream</span>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon
            return (
              <button
                key={item.id}
                onClick={() => setActiveItem(item.id)}
                className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeItem === item.id
                    ? 'bg-red-primary text-white'
                    : 'text-gray-700 hover:bg-white/30'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{item.label}</span>
              </button>
            )
          })}
        </div>

        {/* Library Section */}
        <div className="px-4 py-2">
          <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
            Library
          </h3>
          <div className="space-y-1">
            {libraryItems.map((item) => {
              const Icon = item.icon
              return (
                <button
                  key={item.id}
                  onClick={() => setActiveItem(item.id)}
                  className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                    activeItem === item.id
                      ? 'bg-red-primary text-white'
                      : 'text-gray-600 hover:bg-white/30'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="text-sm">{item.label}</span>
                </button>
              )
            })}
          </div>
        </div>

        {/* Discover Section */}
        <div className="px-4 py-2">
          <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
            Discover
          </h3>
          <div className="space-y-1">
            {discoverItems.map((item) => {
              const Icon = item.icon
              return (
                <button
                  key={item.id}
                  onClick={() => setActiveItem(item.id)}
                  className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                    activeItem === item.id
                      ? 'bg-red-primary text-white'
                      : 'text-gray-600 hover:bg-white/30'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="text-sm">{item.label}</span>
                </button>
              )
            })}
          </div>
        </div>

        {/* Playlists */}
        <div className="px-4 py-2">
          <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
            Playlists
          </h3>
          <div className="space-y-1">
            {playlists.map((playlist, index) => (
              <button
                key={index}
                onClick={() => setActiveItem(`playlist-${index}`)}
                className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                  activeItem === `playlist-${index}`
                    ? 'bg-red-primary text-white'
                    : 'text-gray-600 hover:bg-white/30'
                }`}
              >
                <span className="text-sm">{playlist}</span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
