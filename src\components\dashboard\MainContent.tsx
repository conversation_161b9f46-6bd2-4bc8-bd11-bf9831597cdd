'use client'

import { Play, Heart, MoreHorizontal } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/Button'

interface Track {
  id: string
  title: string
  artist: string
  album: string
  duration: string
  image: string
}

interface MainContentProps {
  onTrackSelect: (track: Track) => void
  currentTrack: Track | null
}

export function MainContent({ onTrackSelect, currentTrack }: MainContentProps) {
  // Mock data for demonstration
  const featuredPlaylists = [
    {
      id: '1',
      title: 'Today\'s Top Hits',
      description: 'The most played songs right now',
      image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop',
    },
    {
      id: '2',
      title: 'Chill Vibes',
      description: 'Relax and unwind with these tracks',
      image: 'https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=300&h=300&fit=crop',
    },
    {
      id: '3',
      title: 'Workout Mix',
      description: 'High energy songs for your workout',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=300&fit=crop',
    },
    {
      id: '4',
      title: 'Jazz Classics',
      description: 'Timeless jazz standards',
      image: 'https://images.unsplash.com/photo-1415201364774-f6f0bb35f28f?w=300&h=300&fit=crop',
    },
  ]

  const recentTracks: Track[] = [
    {
      id: '1',
      title: 'Blinding Lights',
      artist: 'The Weeknd',
      album: 'After Hours',
      duration: '3:20',
      image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=50&h=50&fit=crop',
    },
    {
      id: '2',
      title: 'Watermelon Sugar',
      artist: 'Harry Styles',
      album: 'Fine Line',
      duration: '2:54',
      image: 'https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=50&h=50&fit=crop',
    },
    {
      id: '3',
      title: 'Levitating',
      artist: 'Dua Lipa',
      album: 'Future Nostalgia',
      duration: '3:23',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=50&h=50&fit=crop',
    },
    {
      id: '4',
      title: 'Good 4 U',
      artist: 'Olivia Rodrigo',
      album: 'SOUR',
      duration: '2:58',
      image: 'https://images.unsplash.com/photo-1415201364774-f6f0bb35f28f?w=50&h=50&fit=crop',
    },
    {
      id: '5',
      title: 'Stay',
      artist: 'The Kid LAROI, Justin Bieber',
      album: 'Stay',
      duration: '2:21',
      image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=50&h=50&fit=crop',
    },
  ]

  return (
    <div className="flex-1 overflow-y-auto">
      <div className="p-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Good afternoon
          </h1>
          <p className="text-gray-600">
            Ready to discover your next favorite song?
          </p>
        </div>

        {/* Featured Playlists */}
        <section className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Featured Playlists</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredPlaylists.map((playlist) => (
              <div
                key={playlist.id}
                className="glass rounded-xl p-4 hover:bg-white/40 transition-all duration-200 cursor-pointer group"
              >
                <div className="relative mb-4">
                  <img
                    src={playlist.image}
                    alt={playlist.title}
                    className="w-full aspect-square object-cover rounded-lg"
                  />
                  <button className="absolute bottom-2 right-2 w-12 h-12 bg-red-primary rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:scale-105">
                    <Play className="w-5 h-5 ml-1" />
                  </button>
                </div>
                <h3 className="font-semibold text-gray-900 mb-1">{playlist.title}</h3>
                <p className="text-sm text-gray-600">{playlist.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Recently Played */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Recently Played</h2>
          <div className="glass rounded-xl overflow-hidden">
            <div className="p-4 border-b border-white/20">
              <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-600 uppercase tracking-wider">
                <div className="col-span-1">#</div>
                <div className="col-span-5">Title</div>
                <div className="col-span-3">Album</div>
                <div className="col-span-2">Duration</div>
                <div className="col-span-1"></div>
              </div>
            </div>
            <div className="divide-y divide-white/10">
              {recentTracks.map((track, index) => (
                <div
                  key={track.id}
                  className="p-4 hover:bg-white/20 transition-colors cursor-pointer group"
                  onClick={() => onTrackSelect(track)}
                >
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-1">
                      <span className="text-gray-600 group-hover:hidden">{index + 1}</span>
                      <Play className="w-4 h-4 text-red-primary hidden group-hover:block" />
                    </div>
                    <div className="col-span-5 flex items-center gap-3">
                      <img
                        src={track.image}
                        alt={track.title}
                        className="w-10 h-10 rounded"
                      />
                      <div>
                        <p className="font-medium text-gray-900">{track.title}</p>
                        <p className="text-sm text-gray-600">{track.artist}</p>
                      </div>
                    </div>
                    <div className="col-span-3">
                      <p className="text-gray-600">{track.album}</p>
                    </div>
                    <div className="col-span-2">
                      <p className="text-gray-600">{track.duration}</p>
                    </div>
                    <div className="col-span-1 flex items-center gap-2">
                      <Button variant="ghost" size="sm" className="p-1 opacity-0 group-hover:opacity-100">
                        <Heart className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="p-1 opacity-0 group-hover:opacity-100">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}
